import { GlobalFlawaButton } from "@/components/global-flawa-button"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, X, Flower, HelpCircle } from "lucide-react"
import Link from "next/link"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

// Pricing plans based on the provided JSON
const pricingPlans = [
  {
    name: "Free",
    description: "For unclaimed brand pages",
    price: "$0",
    period: "forever",
    features: [
      { name: "Unclaimed Flawagram page", included: true },
      { name: "Basic brand information", included: true },
      { name: "Up to 50 Flawas", included: true },
      { name: "Public visibility", included: true },
      { name: "Customization options", included: false },
      { name: "Analytics dashboard", included: false },
      { name: "Featured placement", included: false },
      { name: "Priority support", included: false },
    ],
    flawas: "0-50",
    cta: "Claim Your Page",
    popular: false,
  },
  {
    name: "Basic",
    description: "For brands who want to customize their page",
    price: "$10",
    period: "per month",
    features: [
      { name: "Claimed & verified brand page", included: true },
      { name: "Custom logo & description", included: true },
      { name: "Up to 1,000 Flawas", included: true },
      { name: "Highlight favorite Flawas", included: true },
      { name: "Basic analytics", included: true },
      { name: "Social media integration", included: true },
      { name: "Featured placement", included: false },
      { name: "Priority support", included: false },
    ],
    flawas: "Up to 1,000",
    cta: "Get Started",
    popular: true,
  },
  {
    name: "Pro",
    description: "For brands who want maximum visibility",
    price: "$15",
    period: "per month",
    features: [
      { name: "All Basic features", included: true },
      { name: "Unlimited Flawas", included: true },
      { name: "Advanced analytics", included: true },
      { name: "Featured on homepage", included: true },
      { name: "Custom branding options", included: true },
      { name: "Priority support", included: true },
      { name: "API access", included: true },
      { name: "Dedicated account manager", included: true },
    ],
    flawas: "Unlimited",
    cta: "Get Started",
    popular: false,
  },
]

// FAQ items
const faqItems = [
  {
    question: "What happens if I exceed my Flawa limit?",
    answer:
      "If you exceed your Flawa limit on the Free or Basic plan, your oldest Flawas will be archived but not deleted. You can upgrade your plan at any time to increase your limit and restore archived Flawas.",
  },
  {
    question: "Can I downgrade my plan later?",
    answer:
      "Yes, you can downgrade your plan at any time. If you downgrade to a plan with a lower Flawa limit, your oldest Flawas will be archived but not deleted. You can upgrade again at any time to restore them.",
  },
  {
    question: "How does billing work?",
    answer:
      "We offer monthly and annual billing options. Annual billing comes with a 15% discount. You can cancel your subscription at any time, and your plan will remain active until the end of your billing period.",
  },
  {
    question: "Do you offer discounts for non-profits?",
    answer:
      "Yes, we offer a 50% discount on all paid plans for verified non-profit organizations. Please contact our support team to apply for this discount.",
  },
  {
    question: "What payment methods do you accept?",
    answer:
      "We accept all major credit cards and process payments through Flutterwave, a secure payment processor. We do not store your payment information on our servers.",
  },
  {
    question: "Can I try a paid plan before committing?",
    answer:
      "We offer a 14-day free trial for both the Basic and Pro plans. No credit card is required to start your trial, and you can downgrade to the Free plan at any time if you decide not to continue.",
  },
]

export default function PricingPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        <section className="w-full py-12 md:py-24 bg-muted">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center space-y-4 text-center">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl">
                  Simple, Transparent Pricing
                </h1>
                <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
                  Choose the plan that's right for your brand. All plans include a beautiful Flawagram page.
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="w-full py-12 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {pricingPlans.map((plan) => (
                <Card
                  key={plan.name}
                  className={`flex flex-col border-2 ${plan.popular ? "border-black" : "border-border"} rounded-xl shadow-lg`}
                >
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-2xl">{plan.name}</CardTitle>
                      {plan.popular && <Badge className="bg-black hover:bg-black">Most Popular</Badge>}
                    </div>
                    <CardDescription>{plan.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="flex-1">
                    <div className="mb-6">
                      <span className="text-4xl font-bold">{plan.price}</span>
                      <span className="text-text-secondary ml-2">{plan.period}</span>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Flower className="h-5 w-5" />
                        <span className="font-medium">{plan.flawas} Flawas</span>
                      </div>
                      <ul className="space-y-2">
                        {plan.features.map((feature) => (
                          <li key={feature.name} className="flex items-start gap-2">
                            {feature.included ? (
                              <Check className="h-5 w-5 text-green-500 mt-0.5" />
                            ) : (
                              <X className="h-5 w-5 text-gray-300 mt-0.5" />
                            )}
                            <span className={feature.included ? "" : "text-text-secondary"}>{feature.name}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button asChild className={`w-full ${plan.popular ? "bg-black hover:bg-gray-800" : ""}`}>
                      <Link href="/claim">{plan.cta}</Link>
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>

            <div className="mt-20">
              <div className="text-center mb-10">
                <h2 className="text-3xl font-bold">Frequently Asked Questions</h2>
                <p className="text-text-secondary mt-2">
                  Have questions about our pricing? Find answers to common questions below.
                </p>
              </div>
              <div className="max-w-3xl mx-auto">
                <Accordion type="single" collapsible className="w-full">
                  {faqItems.map((item, index) => (
                    <AccordionItem key={index} value={`item-${index}`}>
                      <AccordionTrigger className="text-left">
                        <div className="flex items-center gap-2">
                          <HelpCircle className="h-5 w-5 text-text-secondary" />
                          <span>{item.question}</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="text-text-secondary pl-7">{item.answer}</AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            </div>

            <div className="mt-20 text-center">
              <h2 className="text-2xl font-bold mb-4">Need a custom plan?</h2>
              <p className="text-text-secondary max-w-2xl mx-auto mb-6">
                If you have specific needs or a large brand with multiple products, we offer custom plans tailored to
                your requirements.
              </p>
              <Button asChild variant="outline" className="border-black hover:bg-hover hover:text-white">
                <Link href="/contact">Contact Us</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      <GlobalFlawaButton />
    </div>
  )
}
