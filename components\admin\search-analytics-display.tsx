'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2 } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";


interface SearchTermStat {
  term: string;
  count: number;
}

interface SearchAnalyticsData {
  topSearchTerms: SearchTermStat[];
  noResultSearches: SearchTermStat[];
}

interface ApiResponse extends Partial<SearchAnalyticsData> {
  error?: string;
}

export function SearchAnalyticsDisplay() {
  const [data, setData] = useState<SearchAnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/admin/search-analytics');
        const result: ApiResponse = await response.json();

        if (!response.ok) {
          throw new Error(result.error || `API request failed with status ${response.status}`);
        }
        
        if (result.topSearchTerms && result.noResultSearches) {
            setData({
                topSearchTerms: result.topSearchTerms,
                noResultSearches: result.noResultSearches,
            });
        } else {
            throw new Error("Incomplete search analytics data received from API.");
        }

      } catch (e: any) {
        console.error("Failed to fetch search analytics data:", e);
        setError(e.message || 'An unknown error occurred.');
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Search Analytics</CardTitle>
          <CardDescription>Insights into user search behavior.</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <p className="ml-2">Loading search data...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Search Analytics</CardTitle>
          <CardDescription>Insights into user search behavior.</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Search Analytics</CardTitle>
          <CardDescription>Insights into user search behavior.</CardDescription>
        </CardHeader>
        <CardContent>
          <p>No search analytics data available to display.</p>
        </CardContent>
      </Card>
    );
  }

  const renderTable = (terms: SearchTermStat[], title: string) => (
    <div className="mt-4">
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      {terms.length > 0 ? (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Search Term</TableHead>
              <TableHead className="text-right">Count</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {terms.map((item) => (
              <TableRow key={item.term}>
                <TableCell>{item.term}</TableCell>
                <TableCell className="text-right">{item.count.toLocaleString()}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <p className="text-muted-foreground">No data for this category.</p>
      )}
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Search Analytics</CardTitle>
        <CardDescription>Top search terms and searches with no results.</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="topSearches" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="topSearches">Top Searches</TabsTrigger>
            <TabsTrigger value="noResultSearches">No Result Searches</TabsTrigger>
          </TabsList>
          <TabsContent value="topSearches">
            {renderTable(data.topSearchTerms, "Most Frequent Search Terms")}
          </TabsContent>
          <TabsContent value="noResultSearches">
            {renderTable(data.noResultSearches, "Searches Yielding No Results")}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}