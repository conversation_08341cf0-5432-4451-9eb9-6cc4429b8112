import { NextResponse } from 'next/server';
import {
  updateBrandStatus,
  toggleBrandFeaturedStatus,
  deleteBrand,
  BrandAdminStatus
} from '@/services/brandService';
import { withAdminAuth } from '@/lib/authUtils';
// import { supabase } from '@/lib/supabaseClient'; // No longer needed here

interface RouteParams {
  params: {
    id: string;
  };
}

async function patchHandler(request: Request, { params }: RouteParams) { // Renamed PATCH to patchHandler
  try {
    // Admin check is now handled by withAdminAuth HOF
    const { id: brandId } = params;
    if (!brandId) {
      return NextResponse.json({ error: 'Brand ID is required' }, { status: 400 });
    }

    const body = await request.json();
    const { status, isFeatured } = body;

    let updatedBrand;

    if (status !== undefined) {
      if (!['pending', 'approved', 'rejected'].includes(status)) {
        return NextResponse.json({ error: 'Invalid status provided. Must be "pending", "approved", or "rejected".' }, { status: 400 });
      }
      updatedBrand = await updateBrandStatus(brandId, status as BrandAdminStatus);
    } else if (isFeatured !== undefined) {
      if (typeof isFeatured !== 'boolean') {
        return NextResponse.json({ error: 'Invalid isFeatured value. Must be true or false.' }, { status: 400 });
      }
      updatedBrand = await toggleBrandFeaturedStatus(brandId, isFeatured);
    } else {
      return NextResponse.json({ error: 'No valid action specified (status or isFeatured)' }, { status: 400 });
    }

    if (!updatedBrand) {
      return NextResponse.json({ error: 'Brand not found or failed to update' }, { status: 404 });
    }

    return NextResponse.json(updatedBrand);

  } catch (error) {
    console.error(`Error in PATCH /api/admin/brands/${params.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to update brand', details: errorMessage }, { status: 500 });
  }
}

async function deleteHandler(request: Request, { params }: RouteParams) { // Renamed DELETE to deleteHandler
  try {
    // Admin check is now handled by withAdminAuth HOF
    const { id: brandId } = params;
    if (!brandId) {
      return NextResponse.json({ error: 'Brand ID is required' }, { status: 400 });
    }

    await deleteBrand(brandId);
    return NextResponse.json({ message: 'Brand deleted successfully' }, { status: 200 });
  } catch (error) { // This closing brace for try was missing
    console.error(`Error in DELETE /api/admin/brands/${params.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to delete brand', details: errorMessage }, { status: 500 });
  }
}

export const PATCH = withAdminAuth(patchHandler);
export const DELETE = withAdminAuth(deleteHandler);