import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabaseClient'; // For potential auth or brand validation

// TODO: Implement proper brand ownership/authorization check
// This function should verify that the authenticated user has rights to view analytics for this brandId.
async function canViewBrandAnalytics(req: NextRequest, brandId: string): Promise<boolean> {
  // const { data: { user } } = await supabase.auth.getUser();
  // if (!user) return false;
  //
  // // Example: Check if user is an admin or owns the brand
  // const { data: brandMembership, error } = await supabase
  //   .from('brand_members') // Assuming a table linking users to brands they manage
  //   .select('user_id')
  //   .eq('brand_id', brandId)
  //   .eq('user_id', user.id)
  //   .maybeSingle();
  //
  // if (error) {
  //   console.error("Error checking brand membership:", error);
  //   return false;
  // }
  // return !!brandMembership;
  console.log(`Auth check placeholder for brand ${brandId} and request: ${req.url}`);
  return true; // Placeholder: assumes authorized for now
}

// In a real application, this data would come from your analytics system,
// possibly by querying a table that logs page views with referral information.
const mockReferralData: Record<string, Array<{ source: string; views: number; uniqueVisitors: number }>> = {
  'apple': [
    { source: 'google.com', views: 1200, uniqueVisitors: 800 },
    { source: 'twitter.com', views: 800, uniqueVisitors: 500 },
    { source: 'techcrunch.com', views: 500, uniqueVisitors: 350 },
    { source: 'Direct/Unknown', views: 1500, uniqueVisitors: 1000 },
    { source: 'facebook.com', views: 300, uniqueVisitors: 200 },
  ],
  'nike': [
    { source: 'instagram.com', views: 2500, uniqueVisitors: 1800 },
    { source: 'google.com', views: 1000, uniqueVisitors: 700 },
    { source: 'espn.com', views: 700, uniqueVisitors: 500 },
    { source: 'Direct/Unknown', views: 1200, uniqueVisitors: 900 },
    { source: 'runnersworld.com', views: 400, uniqueVisitors: 300 },
  ],
  // Add more mock data for other brand IDs as needed
};

export async function GET(
  req: NextRequest,
  { params }: { params: { brandId: string } }
) {
  const { brandId } = params;

  if (!brandId) {
    return NextResponse.json({ error: 'Brand ID is required' }, { status: 400 });
  }

  // TODO: Implement proper authorization to ensure the user can access this brand's analytics
  const authorized = await canViewBrandAnalytics(req, brandId);
  if (!authorized) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
  }

  try {
    const referrals = mockReferralData[brandId.toLowerCase()] || [
        { source: 'google.com', views: Math.floor(Math.random() * 500) + 50, uniqueVisitors: Math.floor(Math.random() * 300) + 20 },
        { source: 'Direct/Unknown', views: Math.floor(Math.random() * 800) + 100, uniqueVisitors: Math.floor(Math.random() * 500) + 50 },
        { source: 'Social Media Campaign X', views: Math.floor(Math.random() * 300) + 30, uniqueVisitors: Math.floor(Math.random() * 200) + 10 },
    ];
    
    // Sort by views descending
    referrals.sort((a, b) => b.views - a.views);

    return NextResponse.json({ referralSources: referrals }, { status: 200 });
  } catch (error: any) {
    console.error(`Error fetching referral sources for brand ${brandId}:`, error);
    return NextResponse.json({ error: `Internal server error: ${error.message}` }, { status: 500 });
  }
}