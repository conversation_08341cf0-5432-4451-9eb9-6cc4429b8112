// Simple test script to test the CSV upload API
const fs = require('fs');
const path = require('path');

// Create a test CSV file
const testCSV = `name,domain,description,website_url,category,tier,status,is_featured
"Test Brand 1","testbrand1.com","A test brand for testing","https://testbrand1.com","Technology","Free","approved","false"
"Test Brand 2","testbrand2.com","Another test brand","https://testbrand2.com","E-commerce","Pro","approved","true"
"Test Brand 3","testbrand3.com","Third test brand","https://testbrand3.com","Fashion","Free","pending","false"`;

// Write test CSV file
fs.writeFileSync('test-brands.csv', testCSV);

console.log('Test CSV file created: test-brands.csv');
console.log('Content:');
console.log(testCSV);

console.log('\nTo test the API:');
console.log('1. Start the development server: npm run dev');
console.log('2. Navigate to the admin dashboard');
console.log('3. Go to the "Bulk Operations" tab');
console.log('4. Upload the test-brands.csv file');
