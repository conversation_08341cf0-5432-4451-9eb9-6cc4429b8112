/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  experimental: {
    // Allow cross-origin requests during development
    allowedDevOrigins: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002', 'http://*************:3002'],
  },
}

export default nextConfig
