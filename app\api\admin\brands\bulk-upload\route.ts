import { NextResponse } from 'next/server';
import { withAdminAuth } from '@/lib/authUtils';
import { supabase } from '@/lib/supabaseClient';

interface BrandCSVRow {
  name: string;
  domain: string;
  description?: string;
  website_url?: string;
  category?: string;
  logo_url?: string;
  tier?: string;
  status?: string;
  is_featured?: boolean;
}

interface UploadResult {
  success: boolean;
  created: number;
  errors: Array<{
    row: number;
    error: string;
    data?: any;
  }>;
  duplicates: Array<{
    row: number;
    domain: string;
  }>;
}

async function handler(request: Request): Promise<NextResponse> {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    if (!file.name.endsWith('.csv')) {
      return NextResponse.json({ error: 'File must be a CSV' }, { status: 400 });
    }

    const text = await file.text();
    const lines = text.split('\n').filter(line => line.trim());
    
    if (lines.length < 2) {
      return NextResponse.json({ error: 'CSV must have at least a header and one data row' }, { status: 400 });
    }

    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    
    // Validate required headers
    const requiredHeaders = ['name', 'domain'];
    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
    
    if (missingHeaders.length > 0) {
      return NextResponse.json({ 
        error: `Missing required headers: ${missingHeaders.join(', ')}` 
      }, { status: 400 });
    }

    const result: UploadResult = {
      success: true,
      created: 0,
      errors: [],
      duplicates: []
    };

    // Process each row
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/^"|"$/g, ''));
      
      if (values.length !== headers.length) {
        result.errors.push({
          row: i + 1,
          error: `Row has ${values.length} columns but header has ${headers.length}`
        });
        continue;
      }

      const rowData: any = {};
      headers.forEach((header, index) => {
        rowData[header] = values[index] || null;
      });

      // Validate required fields
      if (!rowData.name || !rowData.domain) {
        result.errors.push({
          row: i + 1,
          error: 'Name and domain are required',
          data: rowData
        });
        continue;
      }

      // Check for existing domain
      const { data: existingBrand } = await supabase
        .from('brands')
        .select('id')
        .eq('domain', rowData.domain)
        .single();

      if (existingBrand) {
        result.duplicates.push({
          row: i + 1,
          domain: rowData.domain
        });
        continue;
      }

      // Prepare brand data
      const brandData: BrandCSVRow = {
        name: rowData.name,
        domain: rowData.domain,
        description: rowData.description || null,
        website_url: rowData.website_url || rowData.website || `https://${rowData.domain}`,
        category: rowData.category || 'Other',
        logo_url: rowData.logo_url || null,
        tier: rowData.tier || 'Free',
        status: rowData.status || 'approved',
        is_featured: rowData.is_featured === 'true' || rowData.is_featured === '1' || false
      };

      // Insert brand
      const { error: insertError } = await supabase
        .from('brands')
        .insert([brandData]);

      if (insertError) {
        result.errors.push({
          row: i + 1,
          error: insertError.message,
          data: brandData
        });
      } else {
        result.created++;
      }
    }

    // Set overall success based on whether any brands were created
    result.success = result.created > 0;

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error in bulk brand upload:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ 
      error: 'Failed to process CSV upload', 
      details: errorMessage 
    }, { status: 500 });
  }
}

export const POST = withAdminAuth(handler);
