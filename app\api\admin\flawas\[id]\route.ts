import { NextResponse } from 'next/server';
import { updateFlawaStatus, deleteFlawa, FlawaStatus, getBrandApprovalAuthority } from '@/services/flawaService';
import { withAdminAuth, checkAdminAuth, checkBrandOwnership } from '@/lib/authUtils';
import { supabase } from '@/lib/supabaseClient';
// import { supabase } from '@/lib/supabaseClient'; // No longer needed here

interface RouteParams {
  params: {
    id: string;
  };
}

async function patchHandler(request: Request, { params }: RouteParams) { // Renamed PATCH to patchHandler
  try {
    const { id: flawaId } = params;
    if (!flawaId) {
      return NextResponse.json({ error: 'Flawa ID is required' }, { status: 400 });
    }

    // First, get the flawa to find its brand
    const { data: flawa, error: flawaError } = await supabase
      .from('flawas')
      .select('brand_id')
      .eq('id', flawaId)
      .single();

    if (flawaError || !flawa) {
      return NextResponse.json({ error: 'Flawa not found' }, { status: 404 });
    }

    // Determine who can approve this flawa based on brand claim status
    const approvalAuthority = await getBrandApprovalAuthority(flawa.brand_id);

    // Check if current user has permission
    const isAdmin = await checkAdminAuth();
    const ownsBrand = await checkBrandOwnership(flawa.brand_id);

    let hasPermission = false;
    if (approvalAuthority === 'admin' && isAdmin) {
      hasPermission = true; // Unclaimed brand, admin can approve
    } else if (approvalAuthority === 'brand' && (isAdmin || ownsBrand)) {
      hasPermission = true; // Claimed brand, brand owner or admin can approve
    }

    if (!hasPermission) {
      return NextResponse.json({
        error: 'Unauthorized: You do not have permission to moderate this flawa',
        details: approvalAuthority === 'admin'
          ? 'This brand is unclaimed. Only sitewide admins can approve flawas.'
          : 'This brand is claimed. Only the brand owner or sitewide admins can approve flawas.'
      }, { status: 403 });
    }

    const body = await request.json();
    const { status } = body;

    if (!status || !['pending', 'approved', 'rejected'].includes(status)) {
      return NextResponse.json({ error: 'Invalid status provided. Must be "pending", "approved", or "rejected".' }, { status: 400 });
    }

    const updatedFlawa = await updateFlawaStatus(flawaId, status as FlawaStatus);

    if (!updatedFlawa) {
      return NextResponse.json({ error: 'Flawa not found or failed to update' }, { status: 404 });
    }

    return NextResponse.json(updatedFlawa);

  } catch (error) {
    console.error(`Error in PATCH /api/admin/flawas/${params.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to update flawa status', details: errorMessage }, { status: 500 });
  }
}

async function deleteHandler(request: Request, { params }: RouteParams) { // Renamed DELETE to deleteHandler
  try {
    // Admin check is now handled by withAdminAuth HOF
    const { id: flawaId } = params;
    if (!flawaId) {
      return NextResponse.json({ error: 'Flawa ID is required' }, { status: 400 });
    }

    await deleteFlawa(flawaId);
    return NextResponse.json({ message: 'Flawa deleted successfully' }, { status: 200 });

  } catch (error) {
    console.error(`Error in DELETE /api/admin/flawas/${params.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to delete flawa', details: errorMessage }, { status: 500 });
  }
}

export const PATCH = patchHandler;
export const DELETE = withAdminAuth(deleteHandler); // Keep admin auth for delete