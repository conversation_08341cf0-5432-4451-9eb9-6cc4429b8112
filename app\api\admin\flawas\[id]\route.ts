import { NextResponse } from 'next/server';
import { updateFlawaStatus, deleteFlawa, FlawaStatus } from '@/services/flawaService';
import { withAdminAuth } from '@/lib/authUtils';
// import { supabase } from '@/lib/supabaseClient'; // No longer needed here

interface RouteParams {
  params: {
    id: string;
  };
}

async function patchHandler(request: Request, { params }: RouteParams) { // Renamed PATCH to patchHandler
  try {
    // Admin check is now handled by withAdminAuth HOF
    const { id: flawaId } = params;
    if (!flawaId) {
      return NextResponse.json({ error: 'Flawa ID is required' }, { status: 400 });
    }

    const body = await request.json();
    const { status } = body;

    if (!status || !['pending', 'approved', 'rejected'].includes(status)) {
      return NextResponse.json({ error: 'Invalid status provided. Must be "pending", "approved", or "rejected".' }, { status: 400 });
    }

    const updatedFlawa = await updateFlawaStatus(flawaId, status as FlawaStatus);

    if (!updatedFlawa) {
      return NextResponse.json({ error: 'Flawa not found or failed to update' }, { status: 404 });
    }

    return NextResponse.json(updatedFlawa);

  } catch (error) {
    console.error(`Error in PATCH /api/admin/flawas/${params.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to update flawa status', details: errorMessage }, { status: 500 });
  }
}

async function deleteHandler(request: Request, { params }: RouteParams) { // Renamed DELETE to deleteHandler
  try {
    // Admin check is now handled by withAdminAuth HOF
    const { id: flawaId } = params;
    if (!flawaId) {
      return NextResponse.json({ error: 'Flawa ID is required' }, { status: 400 });
    }

    await deleteFlawa(flawaId);
    return NextResponse.json({ message: 'Flawa deleted successfully' }, { status: 200 });

  } catch (error) {
    console.error(`Error in DELETE /api/admin/flawas/${params.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to delete flawa', details: errorMessage }, { status: 500 });
  }
}

export const PATCH = withAdminAuth(patchHandler);
export const DELETE = withAdminAuth(deleteHandler);