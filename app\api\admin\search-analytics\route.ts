import { NextRequest, NextResponse } from 'next/server';

// TODO: Implement proper admin role check
const isAdmin = (req: NextRequest) => {
  console.log("Admin check placeholder for request:", req.url);
  return true; // Placeholder
};

// In a real application, this data would come from a database
// where search queries and their outcomes (results/no results) are logged.
const mockSearchAnalyticsData = {
  topSearchTerms: [
    { term: 'Nike', count: 150 },
    { term: 'Apple', count: 120 },
    { term: 'Local Coffee Shop', count: 90 },
    { term: 'Sustainable Fashion', count: 75 },
    { term: 'Tesla Model Y', count: 60 },
  ],
  noResultSearches: [
    { term: 'Obscure Brand X', count: 25 },
    { term: 'TypoBrandd', count: 18 },
    { term: 'NonExistent Service', count: 15 },
    { term: 'HyperLocal Niche Store', count: 12 },
    { term: 'Random Gibberish', count: 10 },
  ],
};

export async function GET(req: NextRequest) {
  if (!isAdmin(req)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // In a real app, you'd query your database/analytics service here.
    // For now, we return mock data.
    return NextResponse.json(mockSearchAnalyticsData, { status: 200 });
  } catch (error: any) {
    console.error('Error in search analytics API:', error);
    return NextResponse.json({ error: `Internal server error: ${error.message}` }, { status: 500 });
  }
}