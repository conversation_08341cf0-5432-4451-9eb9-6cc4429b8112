"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Flower } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { SubmissionForm } from "@/components/submission-form"

interface GlobalFlawaButtonProps {
  brand?: {
    id: string
    [key: string]: any
  }
}

export function GlobalFlawaButton({ brand }: GlobalFlawaButtonProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          className="global-flawa-button rounded-full p-3 sm:p-4 opacity-95 hover:opacity-100 bg-black text-white hover:bg-gray-800 border border-black"
          aria-label="Send a Flawa"
        >
          <Flower className="h-5 w-5" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Send a Flawa{brand ? ` for ${brand.name}` : ''}</DialogTitle>
          <DialogDescription>Share your love for {brand ? brand.name : 'a brand'}. No account required.</DialogDescription>
        </DialogHeader>
        <SubmissionForm inDialog={true} selectedBrandId={brand?.id} />
      </DialogContent>
    </Dialog>
  )
}
