"use client"

import { useState, FormEvent } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from '@/components/ui/textarea'
import { toast } from "sonner"
import { useAuth } from '@/components/auth-provider'
import { supabase } from '@/lib/supabaseClient' // Assuming you'll save suggestions to Supabase
import Link from 'next/link'

export default function SuggestBrandPage() {
  const { user } = useAuth()
  const [brandName, setBrandName] = useState('')
  const [brandWebsite, setBrandWebsite] = useState('')
  const [reason, setReason] = useState('')
  const [loading, setLoading] = useState(false)
  const [submitted, setSubmitted] = useState(false)

  const handleSubmit = async (event: FormEvent) => {
    event.preventDefault()
    setLoading(true)

    if (!brandName.trim()) {
      toast.error("Please enter the brand name.")
      setLoading(false)
      return
    }

    try {
      // Here you would typically send data to your backend/Supabase
      // For example, inserting into a 'brand_suggestions' table:
      /*
      const { error } = await supabase.from('brand_suggestions').insert([
        {
          brand_name: brandName,
          brand_website: brandWebsite || null,
          reason: reason || null,
          suggested_by_user_id: user?.id || null,
          status: 'pending', // Default status
        },
      ])

      if (error) {
        throw error
      }
      */

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast.success(`Thank you for suggesting ${brandName}! We'll review it.`)
      setSubmitted(true)
      // Optionally clear form:
      // setBrandName('')
      // setBrandWebsite('')
      // setReason('')
    } catch (error: any) {
      console.error("Error submitting brand suggestion:", error)
      toast.error(error.message || "Failed to submit your suggestion. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  if (submitted) {
    return (
      <div className="container mx-auto max-w-xl py-12">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center text-green-600">💡 Suggestion Received!</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-lg">Thanks for helping us grow Flawagram!</p>
            <p>We've received your suggestion for <strong>{brandName}</strong> and our team will review it shortly.</p>
            <Button asChild>
              <Link href="/">Back to Home</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto max-w-xl py-8 px-4 sm:px-6 lg:px-8">
      <Card className="shadow-xl">
        <CardHeader>
          <CardTitle className="text-3xl font-bold tracking-tight text-center">Suggest a Brand</CardTitle>
          <CardDescription className="text-center text-muted-foreground mt-1">
            Help us grow Flawagram! If you know a brand that deserves to be here, let us know.
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6 pt-6">
            <div className="space-y-2">
              <Label htmlFor="brandName">Brand Name <span className="text-red-500">*</span></Label>
              <Input
                id="brandName"
                placeholder="e.g., The Local Bakery"
                value={brandName}
                onChange={(e) => setBrandName(e.target.value)}
                required
                disabled={loading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="brandWebsite">Brand Website or Social Media Link (Optional)</Label>
              <Input
                id="brandWebsite"
                type="url"
                placeholder="https://thelocalbakery.com or https://instagram.com/localbakery"
                value={brandWebsite}
                onChange={(e) => setBrandWebsite(e.target.value)}
                disabled={loading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="reason">Why are you suggesting this brand? (Optional)</Label>
              <Textarea
                id="reason"
                placeholder="e.g., They have amazing customer service!"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                rows={3}
                disabled={loading}
              />
            </div>
            {user && (
              <p className="text-sm text-muted-foreground">
                You are suggesting this as: {user.email}
              </p>
            )}
          </CardContent>
          <CardFooter className="flex justify-center pt-8">
            <Button type="submit" className="w-full sm:w-auto" disabled={loading}>
              {loading ? 'Submitting...' : 'Submit Suggestion'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}