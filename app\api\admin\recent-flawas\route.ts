import { NextResponse } from 'next/server';
import { fetchRecentFlawas } from '@/services/flawaService';
import { withAdminAuth } from '@/lib/authUtils';
// import { supabase } from '@/lib/supabaseClient'; // No longer needed here

async function handler(request: Request) { // Renamed from GET to handler
  try {
    // Admin check is now handled by withAdminAuth HOF
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '5', 10);

    if (isNaN(limit) || limit <= 0) {
      return NextResponse.json({ error: 'Invalid limit parameter' }, { status: 400 });
    }

    const recentFlawas = await fetchRecentFlawas(limit);
    return NextResponse.json(recentFlawas);

  } catch (error) {
    console.error('Error in GET /api/admin/recent-flawas:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to fetch recent flawas', details: errorMessage }, { status: 500 });
  }
}

export const GET = withAdminAuth(handler);