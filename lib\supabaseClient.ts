import { createClient } from '@supabase/supabase-js'

// Ensure your environment variables are set up
// For local development, you can create a .env.local file:
// NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
// NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl) {
  console.warn("Supabase URL is not defined. Please check your environment variables: NEXT_PUBLIC_SUPABASE_URL")
}
if (!supabaseAnonKey) {
  console.warn("Supabase Anon Key is not defined. Please check your environment variables: NEXT_PUBLIC_SUPABASE_ANON_KEY")
}

// Create a single supabase client for interacting with your database
export const supabase = createClient(supabaseUrl!, supabaseAnonKey!)

// Example of how to use it for auth state changes:
// export const onAuthStateChange = (callback: (session: Session | null) => void) => {
//   const { data: authListener } = supabase.auth.onAuthStateChange((_event, session) => {
//     callback(session);
//   });
//   return () => {
//     authListener?.subscription.unsubscribe();
//   };
// };