import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();
    const { message, maxLength = 280 } = body;

    // Validate the request
    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Validate message length
    if (message.length > maxLength) {
      return NextResponse.json(
        { error: `Message exceeds maximum length of ${maxLength} characters` },
        { status: 400 }
      );
    }

    // Get API key from environment variables (server-side only)
    const apiKey = process.env.GEMINI_API_KEY;

    if (!apiKey) {
      console.error("Gemini API key is not configured");
      return NextResponse.json(
        { error: 'API key not configured' },
        { status: 500 }
      );
    }

    // Prepare the prompt for Gemini
    const prompt = `
      Enhance the following message by adding 1-3 relevant emojis.
      Keep the message approximately the same length as the original.
      The enhanced message must not exceed ${maxLength} characters.
      Only return the enhanced message, nothing else.

      Original message: "${message}"
    `;

    // Set up timeout for the API call
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    try {
      // Call the Gemini API
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  { text: prompt }
                ]
              }
            ]
          }),
          signal: controller.signal
        }
      );

      clearTimeout(timeoutId); // Clear timeout if request completes

      // Parse the response
      const data = await response.json();

      // Check for errors in the API response
      if (!response.ok) {
        console.error("Gemini API error:", data);
        return NextResponse.json(
          { error: data.error?.message || 'Failed to enhance message' },
          { status: response.status }
        );
      }

      // Extract the enhanced message from the response
      const enhancedMessage = data.candidates?.[0]?.content?.parts?.[0]?.text || message;

      // Validate the response
      if (!enhancedMessage) {
        console.error("Invalid Gemini API response:", data);
        return NextResponse.json(
          { error: 'Invalid response from Gemini API' },
          { status: 500 }
        );
      }

      // Ensure the enhanced message doesn't exceed the maximum length
      const trimmedMessage = enhancedMessage.slice(0, maxLength);

      return NextResponse.json({ text: trimmedMessage });
    } catch (error) {
      // Handle timeout errors
      if (error instanceof DOMException && error.name === 'AbortError') {
        console.error("Gemini API request timed out");
        return NextResponse.json(
          { error: 'Request to Gemini API timed out' },
          { status: 504 }
        );
      }

      // Re-throw for the outer catch block
      throw error;
    }
  } catch (error) {
    console.error("Error enhancing message:", error);
    return NextResponse.json(
      { error: 'Failed to enhance message' },
      { status: 500 }
    );
  }
}
