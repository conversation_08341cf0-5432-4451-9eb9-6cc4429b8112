import { GlobalFlawaButton } from "@/components/global-flawa-button"
import { <PERSON>, Heart, Star, Award, Users, Globe } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"

export default function AboutPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        <section className="w-full py-12 md:py-24 bg-muted">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center space-y-4 text-center">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl">
                  About Flawagram
                </h1>
                <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
                  Where brands celebrate all the love they receive — collected in one beautiful, curated space.
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="w-full py-12 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="grid gap-10 md:gap-16">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-4">
                  <h2 className="text-3xl font-bold">Our Mission</h2>
                  <p className="text-text-secondary text-lg">
                    Flawagram was created with a simple mission: to collect and organize all the love that brands
                    receive across the internet into one beautiful, curated space.
                  </p>
                  <p className="text-text-secondary text-lg">
                    We believe that positive feedback and genuine praise should be celebrated and shared. In a world
                    where criticism often gets the spotlight, we're creating a platform that highlights the good
                    experiences, the moments of delight, and the genuine appreciation that customers have for the brands
                    they love.
                  </p>
                </div>
                <div className="relative h-[300px] md:h-[400px] rounded-xl overflow-hidden">
                  <Image
                    src="/placeholder.svg?height=400&width=600"
                    alt="Flawagram mission"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h2 className="text-3xl font-bold">How It Works</h2>
                <div className="grid gap-6 md:grid-cols-3">
                  <div className="space-y-3 p-6 bg-muted rounded-xl">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-black text-white">
                      <span className="text-xl font-bold">1</span>
                    </div>
                    <h3 className="text-xl font-bold">Discover</h3>
                    <p className="text-text-secondary">
                      Browse through brands and see the love they receive from their customers and fans. Each brand has
                      its own Flawagram page showcasing curated praise from across the internet.
                    </p>
                  </div>
                  <div className="space-y-3 p-6 bg-muted rounded-xl">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-black text-white">
                      <span className="text-xl font-bold">2</span>
                    </div>
                    <h3 className="text-xl font-bold">Share</h3>
                    <p className="text-text-secondary">
                      Send a Flawa to your favorite brands by submitting your own praise, images, or links. Our team
                      reviews each submission to ensure quality and authenticity before publishing.
                    </p>
                  </div>
                  <div className="space-y-3 p-6 bg-muted rounded-xl">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-black text-white">
                      <span className="text-xl font-bold">3</span>
                    </div>
                    <h3 className="text-xl font-bold">Celebrate</h3>
                    <p className="text-text-secondary">
                      Brands can claim their Flawagram page to customize and highlight their favorite praise. Claimed
                      pages get additional features and visibility on our platform.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h2 className="text-3xl font-bold">What is a Flawa?</h2>
                <div className="flex items-center gap-2 mb-4">
                  <Flower className="h-6 w-6" />
                  <span className="text-xl font-medium">Flawa = Flow of Appreciation</span>
                </div>
                <p className="text-text-secondary text-lg">
                  A "Flawa" is our term for any expression of love, praise, or positive feedback for a brand. Flawas can
                  come in many forms:
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <ul className="list-none space-y-2 text-text-secondary">
                    <li className="flex items-center gap-2">
                      <Heart className="h-5 w-5 text-black" />
                      Social media posts (Twitter, Facebook, Instagram)
                    </li>
                    <li className="flex items-center gap-2">
                      <Star className="h-5 w-5 text-black" />
                      Customer reviews and testimonials
                    </li>
                    <li className="flex items-center gap-2">
                      <Award className="h-5 w-5 text-black" />
                      Video testimonials and unboxing reactions
                    </li>
                  </ul>
                  <ul className="list-none space-y-2 text-text-secondary">
                    <li className="flex items-center gap-2">
                      <Globe className="h-5 w-5 text-black" />
                      Blog mentions and press coverage
                    </li>
                    <li className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-black" />
                      Direct submissions from fans and customers
                    </li>
                    <li className="flex items-center gap-2">
                      <Flower className="h-5 w-5 text-black" />
                      Any other form of genuine appreciation
                    </li>
                  </ul>
                </div>
                <p className="text-text-secondary text-lg mt-4">
                  Our team curates Flawas from across the internet, verifies them, and organizes them by brand to create
                  a beautiful showcase of appreciation. We ensure that only authentic, positive feedback is featured on
                  our platform.
                </p>
              </div>

              <div className="space-y-4 bg-muted p-8 rounded-xl">
                <h2 className="text-3xl font-bold">For Brands</h2>
                <p className="text-text-secondary text-lg">
                  If you're a brand, your Flawagram already exists! We collect and curate praise for brands across the
                  internet. You can claim your Flawagram page to:
                </p>
                <div className="grid md:grid-cols-2 gap-6 mt-6">
                  <div className="bg-white p-6 rounded-lg shadow-sm">
                    <h3 className="text-xl font-bold mb-4">Free Features</h3>
                    <ul className="space-y-2 text-text-secondary">
                      <li className="flex items-center gap-2">
                        <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Basic brand profile
                      </li>
                      <li className="flex items-center gap-2">
                        <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Curated Flawas from across the web
                      </li>
                      <li className="flex items-center gap-2">
                        <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Public visibility on Flawagram
                      </li>
                    </ul>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow-sm">
                    <h3 className="text-xl font-bold mb-4">Premium Features</h3>
                    <ul className="space-y-2 text-text-secondary">
                      <li className="flex items-center gap-2">
                        <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Customizable brand profile
                      </li>
                      <li className="flex items-center gap-2">
                        <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Highlight favorite Flawas
                      </li>
                      <li className="flex items-center gap-2">
                        <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Analytics and engagement metrics
                      </li>
                      <li className="flex items-center gap-2">
                        <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Featured placement on homepage
                      </li>
                      <li className="flex items-center gap-2">
                        <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Respond to and share Flawas
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="flex justify-center mt-6">
                  <Button asChild className="px-8">
                    <Link href="/claim">Claim Your Brand Page</Link>
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                <h2 className="text-3xl font-bold">Our Team</h2>
                <p className="text-text-secondary text-lg">
                  Flawagram was founded by a team of brand enthusiasts who believe in the power of positive feedback.
                  We're passionate about helping brands celebrate the love they receive and making it easier for fans to
                  express their appreciation.
                </p>
                <div className="grid md:grid-cols-3 gap-8 mt-8">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="text-center">
                      <div className="mx-auto rounded-full overflow-hidden w-32 h-32 mb-4">
                        <Image
                          src={`/placeholder.svg?height=128&width=128&text=Team+Member+${i}`}
                          alt={`Team member ${i}`}
                          width={128}
                          height={128}
                          className="object-cover"
                        />
                      </div>
                      <h3 className="text-xl font-bold">Team Member {i}</h3>
                      <p className="text-text-secondary">Co-Founder & {i === 1 ? "CEO" : i === 2 ? "CTO" : "CMO"}</p>
                      <p className="text-text-secondary mt-2">
                        Passionate about connecting brands with their fans and celebrating positive experiences.
                      </p>
                    </div>
                  ))}
                </div>
                <p className="text-text-secondary text-lg mt-8">
                  Our curation team works tirelessly to find and verify Flawas from across the internet, ensuring that
                  only genuine praise is featured on our platform. We're committed to creating a positive space where
                  brands and their fans can connect and celebrate together.
                </p>
              </div>

              <div className="text-center mt-8">
                <h2 className="text-3xl font-bold mb-6">Join the Flawagram Community</h2>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button asChild className="px-8">
                    <Link href="/claim">Claim Your Brand</Link>
                  </Button>
                  <Button asChild variant="outline" className="px-8">
                    <Link href="/contact">Contact Us</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <GlobalFlawaButton />
    </div>
  )
}
