import { supabase } from '@/lib/supabaseClient';
import { Flawa } from '@/types';

/**
 * Fetches all flawas from the database, joining with brand information.
 * @returns A promise that resolves to an array of Flawa objects.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchAllFlawas = async (): Promise<Flawa[]> => {
  const { data, error } = await supabase
    .from('flawas')
    .select(`
      id,
      type,
      content,
      author,
      author_image_url,
      likes,
      views,
      source_url,
      image_url,
      submitted_at,
      social_links,
      user_id,
      status,
      tags,
      brands (
        name,
        domain
      )
    `)
    .order('submitted_at', { ascending: false });

  if (error) {
    console.error('Error fetching all flawas:', error);
    throw new Error(error.message);
  }

  if (!data) {
    return [];
  }

  // Map Supabase data to Flawa type
  return data.map((item: any) => {
    const flawa: Flawa = {
      id: item.id,
      type: item.type,
      content: item.content,
      author: item.author,
      authorImage: item.author_image_url,
      // The Flawa type expects 'brand' to be the brand's name (string)
      // and 'brandDomain' to be the brand's domain (string).
      // The 'brands' object from Supabase contains these.
      brand: item.brands?.name || 'Unknown Brand', // Default if brand is not found
      brandDomain: item.brands?.domain || '', // Default if brand domain is not found
      likes: item.likes,
      views: item.views,
      source: item.source_url,
      image: item.image_url,
      timestamp: item.submitted_at,
      socialLinks: item.social_links,
      user_id: item.user_id,
      status: item.status,
      tags: item.tags,
      // isLiked is not directly fetched here, it usually depends on the current user context
    };
    return flawa;
  });
};

// Additional service functions will be added below:
// - fetchFlawasByBrand
// - fetchFeaturedFlawas (or similar, e.g., recent, top)
// - createFlawa

/**
 * Fetches flawas for a specific brand with optional filtering.
 * @param brandId The UUID of the brand.
 * @param status Optional status filter ('pending', 'approved', 'rejected').
 * @param limit Optional limit for number of results.
 * @returns A promise that resolves to an array of Flawa objects for the specified brand.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchFlawasByBrandId = async (brandId: string, status?: FlawaStatus, limit?: number): Promise<Flawa[]> => {
  if (!brandId) {
    console.warn('fetchFlawasByBrandId called without brandId');
    return [];
  }

  let query = supabase
    .from('flawas')
    .select(`
      id,
      type,
      content,
      author,
      author_image_url,
      likes,
      views,
      source_url,
      image_url,
      submitted_at,
      social_links,
      user_id,
      status,
      tags,
      is_demo,
      brands (
        name,
        domain
      )
    `)
    .eq('brand_id', brandId)
    .order('submitted_at', { ascending: false });

  if (status) {
    query = query.eq('status', status);
  }

  if (limit) {
    query = query.limit(limit);
  }

  const { data, error } = await query;

  if (error) {
    console.error(`Error fetching flawas for brand ${brandId}:`, error);
    throw new Error(error.message);
  }

  if (!data) {
    return [];
  }

  // Map Supabase data to Flawa type
  return data.map((item: any) => {
    const flawa: Flawa = {
      id: item.id,
      type: item.type,
      content: item.content,
      author: item.author,
      authorImage: item.author_image_url,
      brand: item.brands?.name || 'Unknown Brand',
      brandDomain: item.brands?.domain || '',
      likes: item.likes,
      views: item.views,
      source: item.source_url,
      image: item.image_url,
      timestamp: item.submitted_at,
      socialLinks: item.social_links,
      user_id: item.user_id,
      status: item.status,
      tags: item.tags,
      isDemo: item.is_demo,
    };
    return flawa;
  });
};

/**
 * Fetches all flawas for a specific brand from the database.
 * @param brandId The UUID of the brand.
 * @returns A promise that resolves to an array of Flawa objects for the specified brand.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchFlawasByBrand = async (brandId: string): Promise<Flawa[]> => {
  if (!brandId) {
    console.warn('fetchFlawasByBrand called without brandId');
    return [];
  }

  const { data, error } = await supabase
    .from('flawas')
    .select(`
      id,
      type,
      content,
      author,
      author_image_url,
      likes,
      views,
      source_url,
      image_url,
      submitted_at,
      social_links,
      user_id,
      status,
      tags,
      brands (
        name,
        domain
      )
    `)
    .eq('brand_id', brandId)
    .order('submitted_at', { ascending: false });

  if (error) {
    console.error(`Error fetching flawas for brand ${brandId}:`, error);
    throw new Error(error.message);
  }

  if (!data) {
    return [];
  }

  // Map Supabase data to Flawa type
  return data.map((item: any) => {
    const flawa: Flawa = {
      id: item.id,
      type: item.type,
      content: item.content,
      author: item.author,
      authorImage: item.author_image_url,
      brand: item.brands?.name || 'Unknown Brand',
      brandDomain: item.brands?.domain || '',
      likes: item.likes,
      views: item.views,
      source: item.source_url,
      image: item.image_url,
      timestamp: item.submitted_at,
      socialLinks: item.social_links,
      user_id: item.user_id,
      status: item.status,
      tags: item.tags,
    };
    return flawa;
  });
};

/**
 * Interface for the data required to create a new Flawa.
 * Based on the fields in the SubmissionForm and the Supabase schema.
 */
export interface CreateFlawaData {
  type: "tweet" | "image" | "video" | "text";
  content: string;
  author: string; // Assuming author name is collected or derived
  author_image_url?: string;
  brand_id: string; // UUID of the brand
  source_url?: string;
  image_url?: string;
  social_links?: { [key: string]: string };
  user_id?: string | null; // UUID of the submitting user, if logged in
  tags?: string[];
  // 'status' will default to 'pending' in the database or can be set here if needed
  // 'likes' and 'views' will default to 0
}

/**
 * Creates a new flawa in the database.
 * @param flawaData The data for the new flawa.
 * @returns A promise that resolves to the newly created Flawa object.
 * @throws Throws an error if the creation operation fails.
 */
export const createFlawa = async (flawaData: CreateFlawaData): Promise<Flawa> => {
  const { data, error } = await supabase
    .from('flawas')
    .insert([
      {
        type: flawaData.type,
        content: flawaData.content,
        author: flawaData.author,
        author_image_url: flawaData.author_image_url,
        brand_id: flawaData.brand_id,
        source_url: flawaData.source_url,
        image_url: flawaData.image_url,
        social_links: flawaData.social_links,
        user_id: flawaData.user_id,
        tags: flawaData.tags,
        // status: 'pending', // Can be set here or rely on DB default
      },
    ])
    .select(`
      id,
      type,
      content,
      author,
      author_image_url,
      likes,
      views,
      source_url,
      image_url,
      submitted_at,
      social_links,
      user_id,
      status,
      tags,
      brands (
        name,
        domain
      )
    `)
    .single(); // Expecting a single row to be returned after insert

  if (error) {
    console.error('Error creating flawa:', error);
    throw new Error(error.message);
  }

  if (!data) {
    throw new Error('Failed to create flawa: No data returned.');
  }

  // Map the single returned item to Flawa type
  const item = data as any;
  return {
    id: item.id,
    type: item.type,
    content: item.content,
    author: item.author,
    authorImage: item.author_image_url,
    brand: item.brands?.name || 'Unknown Brand',
    brandDomain: item.brands?.domain || '',
    likes: item.likes,
    views: item.views,
    source: item.source_url,
    image: item.image_url,
    timestamp: item.submitted_at,
    socialLinks: item.social_links,
    user_id: item.user_id,
    status: item.status,
    tags: item.tags,
  };
};

/**
 * Fetches a limited number of recent, approved flawas.
 * @param limit The maximum number of recent flawas to fetch.
 * @returns A promise that resolves to an array of Flawa objects.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchRecentApprovedFlawas = async (limit: number = 6): Promise<Flawa[]> => {
  const { data, error } = await supabase
    .from('flawas')
    .select(`
      id,
      type,
      content,
      author,
      author_image_url,
      likes,
      views,
      source_url,
      image_url,
      submitted_at,
      social_links,
      user_id,
      status,
      tags,
      brands (
        name,
        domain
      )
    `)
    .eq('status', 'approved') // Only show approved flawas
    .order('submitted_at', { ascending: false }) // Order by most recent
    .limit(limit);

  if (error) {
    console.error('Error fetching recent approved flawas:', error);
    throw new Error(error.message);
  }

  if (!data) {
    return [];
  }

  return data.map((item: any) => {
    const flawa: Flawa = {
      id: item.id,
      type: item.type,
      content: item.content,
      author: item.author,
      authorImage: item.author_image_url,
      brand: item.brands?.name || 'Unknown Brand',
      brandDomain: item.brands?.domain || '',
      likes: item.likes,
      views: item.views,
      source: item.source_url,
      image: item.image_url,
      timestamp: item.submitted_at,
      socialLinks: item.social_links,
      user_id: item.user_id,
      status: item.status,
      tags: item.tags,
    };
    return flawa;
  });
};

/**
 * Fetches featured flawas (e.g., top-liked, recent).
 * For this example, it fetches a limited number of flawas ordered by likes and then submission date.
 * @param limit The maximum number of featured flawas to fetch.
 * @returns A promise that resolves to an array of featured Flawa objects.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchFeaturedFlawas = async (limit: number = 6): Promise<Flawa[]> => {
  const { data, error } = await supabase
    .from('flawas')
    .select(`
      id,
      type,
      content,
      author,
      author_image_url,
      likes,
      views,
      source_url,
      image_url,
      submitted_at,
      social_links,
      user_id,
      status,
      tags,
      brands (
        name,
        domain
      )
    `)
    .eq('status', 'approved') // Only show approved flawas as featured
    .order('likes', { ascending: false })
    .order('submitted_at', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('Error fetching featured flawas:', error);
    throw new Error(error.message);
  }

  if (!data) {
    return [];
  }

  return data.map((item: any) => {
    const flawa: Flawa = {
      id: item.id,
      type: item.type,
      content: item.content,
      author: item.author,
      authorImage: item.author_image_url,
      brand: item.brands?.name || 'Unknown Brand',
      brandDomain: item.brands?.domain || '',
      likes: item.likes,
      views: item.views,
      source: item.source_url,
      image: item.image_url,
      timestamp: item.submitted_at,
      socialLinks: item.social_links,
      user_id: item.user_id,
      status: item.status,
      tags: item.tags,
    };
    return flawa;
  });
};

/**
 * Fetches the total count of all flawas.
 * @returns A promise that resolves to the total number of flawas.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchTotalFlawasCount = async (): Promise<number> => {
  const { count, error } = await supabase
    .from('flawas')
    .select('*', { count: 'exact', head: true });

  if (error) {
    console.error('Error fetching total flawas count:', error);
    throw new Error(error.message);
  }
  return count ?? 0;
};

/**
 * Fetches the count of flawas with 'pending' status.
 * @returns A promise that resolves to the number of pending flawas.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchPendingFlawasCount = async (): Promise<number> => {
  const { count, error } = await supabase
    .from('flawas')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'pending');

  if (error) {
    console.error('Error fetching pending flawas count:', error);
    throw new Error(error.message);
  }
  return count ?? 0;
};

/**
 * Fetches the count of flawas submitted in a given month.
 * Uses the 'submitted_at' timestamp column.
 * @param date The date to determine the month and year.
 * @returns A promise that resolves to the number of flawas submitted in that month.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchFlawasSubmittedInMonthCount = async (date: Date): Promise<number> => {
  const year = date.getFullYear();
  const month = date.getMonth(); // 0-indexed

  const firstDayOfMonth = new Date(year, month, 1).toISOString();
  const firstDayOfNextMonth = new Date(year, month + 1, 1).toISOString();

  const { count, error } = await supabase
    .from('flawas')
    .select('*', { count: 'exact', head: true })
    .gte('submitted_at', firstDayOfMonth)
    .lt('submitted_at', firstDayOfNextMonth);

  if (error) {
    console.error('Error fetching flawas submitted in month count:', error);
    throw new Error(error.message);
  }
  return count ?? 0;
};

/**
 * Fetches a limited number of recent flawas, regardless of status.
 * Includes brand name and domain.
 * @param limit The maximum number of recent flawas to fetch.
 * @returns A promise that resolves to an array of Flawa objects.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchRecentFlawas = async (limit: number = 5): Promise<Flawa[]> => {
  const { data, error } = await supabase
    .from('flawas')
    .select(`
      id,
      type,
      content,
      author,
      author_image_url,
      likes,
      views,
      source_url,
      image_url,
      submitted_at,
      social_links,
      user_id,
      status,
      tags,
      brands (
        name,
        domain
      )
    `)
    .order('submitted_at', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('Error fetching recent flawas:', error);
    throw new Error(error.message);
  }

  if (!data) {
    return [];
  }

  return data.map((item: any) => {
    const flawa: Flawa = {
      id: item.id,
      type: item.type,
      content: item.content,
      author: item.author,
      authorImage: item.author_image_url,
      brand: item.brands?.name || 'Unknown Brand',
      brandDomain: item.brands?.domain || '',
      likes: item.likes,
      views: item.views,
      source: item.source_url,
      image: item.image_url,
      timestamp: item.submitted_at,
      socialLinks: item.social_links,
      user_id: item.user_id,
      status: item.status,
      tags: item.tags,
    };
    return flawa;
  });
};

/**
 * Defines the possible statuses for a Flawa.
 */
export type FlawaStatus = 'pending' | 'approved' | 'rejected';

/**
 * Determines who can approve flawas for a brand based on claim status.
 * @param brandId The ID of the brand.
 * @returns Promise<'admin' | 'brand' | 'none'> - Who has approval authority.
 */
export const getBrandApprovalAuthority = async (brandId: string): Promise<'admin' | 'brand' | 'none'> => {
  try {
    const { data: brand, error } = await supabase
      .from('brands')
      .select('is_claimed, claimed_by_user_id')
      .eq('id', brandId)
      .single();

    if (error) {
      console.error(`Error fetching brand approval authority for ${brandId}:`, error);
      return 'none';
    }

    if (!brand) {
      return 'none';
    }

    // If brand is claimed, brand owner can approve
    // If brand is unclaimed, only sitewide admin can approve
    return brand.is_claimed ? 'brand' : 'admin';

  } catch (error) {
    console.error('Error determining brand approval authority:', error);
    return 'none';
  }
};

/**
 * Updates the status of a specific flawa.
 * @param flawaId The ID of the flawa to update.
 * @param status The new status for the flawa.
 * @returns A promise that resolves to the updated Flawa object or null if not found.
 * @throws Throws an error if the update operation fails.
 */
export const updateFlawaStatus = async (flawaId: string, status: FlawaStatus): Promise<Flawa | null> => {
  const { data, error } = await supabase
    .from('flawas')
    .update({ status: status, updated_at: new Date().toISOString() })
    .eq('id', flawaId)
    .select(`
      id,
      type,
      content,
      author,
      author_image_url,
      likes,
      views,
      source_url,
      image_url,
      submitted_at,
      social_links,
      user_id,
      status,
      tags,
      brands (
        name,
        domain
      )
    `)
    .single();

  if (error) {
    console.error(`Error updating flawa ${flawaId} to status ${status}:`, error);
    throw new Error(error.message);
  }

  if (!data) return null;

  // Map Supabase data to Flawa type
  const item = data as any;
  return {
    id: item.id,
    type: item.type,
    content: item.content,
    author: item.author,
    authorImage: item.author_image_url,
    brand: item.brands?.name || 'Unknown Brand',
    brandDomain: item.brands?.domain || '',
    likes: item.likes,
    views: item.views,
    source: item.source_url,
    image: item.image_url,
    timestamp: item.submitted_at,
    socialLinks: item.social_links,
    user_id: item.user_id,
    status: item.status,
    tags: item.tags,
  };
};

/**
 * Deletes a specific flawa.
 * @param flawaId The ID of the flawa to delete.
 * @returns A promise that resolves when the operation is complete.
 * @throws Throws an error if the delete operation fails.
 */
export const deleteFlawa = async (flawaId: string): Promise<void> => {
  const { error } = await supabase
    .from('flawas')
    .delete()
    .eq('id', flawaId);

  if (error) {
    console.error(`Error deleting flawa ${flawaId}:`, error);
    throw new Error(error.message);
  }
};

/**
 * Fetches the count of flawas moderated (approved or rejected) today.
 * Uses the 'updated_at' timestamp column (ensure this column is auto-updated on status change)
 * and checks for 'approved' or 'rejected' status.
 * @param date The current date.
 * @returns A promise that resolves to the number of flawas moderated today.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchFlawasModeratedTodayCount = async (date: Date): Promise<number> => {
  const startOfToday = new Date(date);
  startOfToday.setHours(0, 0, 0, 0);
  const endOfToday = new Date(date);
  endOfToday.setHours(23, 59, 59, 999);

  const { count, error } = await supabase
    .from('flawas')
    .select('*', { count: 'exact', head: true })
    .in('status', ['approved', 'rejected'])
    .gte('updated_at', startOfToday.toISOString())
    .lte('updated_at', endOfToday.toISOString());

  if (error) {
    console.error('Error fetching flawas moderated today count:', error);
    throw new Error(error.message);
  }
  return count ?? 0;
};