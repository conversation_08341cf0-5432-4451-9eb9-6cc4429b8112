'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2, TrendingUp, TrendingDown, Minus } from 'lucide-react'; // Added icons

interface ComparativeAnalytic {
  metric: string;
  brandValue: number;
  categoryAverage: number;
  differencePercentage: number;
  interpretation: 'higher' | 'lower' | 'same';
}

interface ApiResponse {
  analytics?: ComparativeAnalytic;
  error?: string;
}

interface BrandComparativeAnalyticsProps {
  brandId: string;
}

export function BrandComparativeAnalytics({ brandId }: BrandComparativeAnalyticsProps) {
  const [data, setData] = useState<ComparativeAnalytic | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!brandId) {
      setError("Brand ID is missing.");
      setLoading(false);
      return;
    }

    async function fetchData() {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/brands/${brandId}/comparative-analytics`);
        const result: ApiResponse = await response.json();

        if (!response.ok) {
          throw new Error(result.error || `API request failed with status ${response.status}`);
        }
        
        setData(result.analytics || null);

      } catch (e: any) {
        console.error(`Failed to fetch comparative analytics for brand ${brandId}:`, e);
        setError(e.message || 'An unknown error occurred.');
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [brandId]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Comparative Performance</CardTitle>
          <CardDescription>How your brand stacks up against the category average.</CardDescription>
        </CardHeader>
        <CardContent className="h-[200px] flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <p className="ml-2">Loading comparative data...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Comparative Performance</CardTitle>
          <CardDescription>How your brand stacks up against the category average.</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Comparative Performance</CardTitle>
          <CardDescription>How your brand stacks up against the category average.</CardDescription>
        </CardHeader>
        <CardContent>
          <p>No comparative analytics data available for this brand yet.</p>
        </CardContent>
      </Card>
    );
  }

  const diffPercent = data.differencePercentage;
  const isPositive = diffPercent > 0;
  const isNegative = diffPercent < 0;
  const Icon = isPositive ? TrendingUp : isNegative ? TrendingDown : Minus;
  const colorClass = isPositive ? 'text-green-600' : isNegative ? 'text-red-600' : 'text-muted-foreground';

  return (
    <Card>
      <CardHeader>
        <CardTitle>Comparative Performance: {data.metric}</CardTitle>
        <CardDescription>
          Comparing your brand's performance to the average in your category.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-around p-4 bg-muted/50 rounded-lg">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Your Brand's {data.metric}</p>
            <p className="text-3xl font-bold">{data.brandValue.toLocaleString()}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Category Average</p>
            <p className="text-3xl font-bold">{data.categoryAverage.toLocaleString()}</p>
          </div>
        </div>
        <div className={`flex items-center p-4 rounded-lg border ${isPositive ? 'border-green-200 bg-green-50 dark:bg-green-900/30' : isNegative ? 'border-red-200 bg-red-50 dark:bg-red-900/30' : 'border-gray-200 bg-gray-50 dark:bg-gray-700/30'}`}>
          <Icon className={`h-10 w-10 mr-3 ${colorClass}`} />
          <div>
            <p className={`text-xl font-semibold ${colorClass}`}>
              {Math.abs(diffPercent).toFixed(1)}% {data.interpretation === 'higher' ? 'Above' : data.interpretation === 'lower' ? 'Below' : 'Same as'} Average
            </p>
            <p className="text-sm text-muted-foreground">
              Your brand's {data.metric.toLowerCase()} is {Math.abs(diffPercent).toFixed(1)}% {data.interpretation} than the category average.
            </p>
          </div>
        </div>
        <p className="text-xs text-muted-foreground text-center pt-2">
          Note: Comparative analytics are based on anonymized data from brands in the same category.
        </p>
      </CardContent>
    </Card>
  );
}