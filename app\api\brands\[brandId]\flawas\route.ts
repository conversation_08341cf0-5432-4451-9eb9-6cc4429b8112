import { NextRequest, NextResponse } from 'next/server';
import { fetchFlawasByBrandId } from '@/services/flawaService';
import { withBrandAuth } from '@/lib/authUtils';

interface RouteParams {
  params: {
    brandId: string;
  };
}

async function handler(request: NextRequest, { params }: RouteParams) {
  try {
    const { brandId } = params;

    if (!brandId) {
      return NextResponse.json({ error: 'Brand ID is required' }, { status: 400 });
    }

    // Get query parameters for filtering
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status'); // optional filter by status
    const limit = parseInt(searchParams.get('limit') || '50', 10);

    const flawas = await fetchFlawasByBrandId(brandId, status as any, limit);

    return NextResponse.json(flawas);

  } catch (error) {
    console.error('Error in GET /api/brands/[brandId]/flawas:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to fetch brand flawas', details: errorMessage }, { status: 500 });
  }
}

export const GET = withBrandAuth(handler);
