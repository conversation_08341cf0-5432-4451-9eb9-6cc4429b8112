import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Flower } from "lucide-react"
// import { getFeaturedBrands } from "@/data/brands" // Will be replaced by service
import { Brand } from "@/types"
import { formatCount, getFaviconUrl } from "@/lib/utils" // Still needed for rendering
import { fetchFeaturedBrandsFromService } from "@/services/brandService"
import { Skeleton } from "@/components/ui/skeleton" // For loading state (though less critical for RSC)

export async function FeaturedBrands() {
  // Fetch featured brands using the service
  // Adding a try-catch block for robustness in server components
  let featuredBrands: Brand[] = [];
  let errorLoading: string | null = null;

  try {
    featuredBrands = await fetchFeaturedBrandsFromService(4); // Fetch top 4 featured brands
  } catch (error) {
    console.error("Failed to fetch featured brands:", error);
    errorLoading = error instanceof Error ? error.message : "Could not load featured brands.";
    // Optionally, you could return a more specific error component or message here
  }


  return (
    <section className="w-full py-12 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Featured Brands</h2>
            <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
              Discover brands with the most love and engagement on Flawagram.
            </p>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mt-8">
          {errorLoading && (
            <div className="col-span-full text-center text-red-600">
              <p>Error: {errorLoading}</p>
            </div>
          )}
          {!errorLoading && featuredBrands.length === 0 && (
             <div className="col-span-full text-center text-text-secondary">
               <p>No featured brands available at the moment.</p>
             </div>
          )}
          {!errorLoading && featuredBrands.map((brand) => (
            <Link key={brand.id} href={`/${brand.id}`} className="hover-scale">
              <Card className="overflow-hidden border-border shadow-card rounded-xl h-full flex flex-col">
                <CardContent className="p-6 flex flex-col items-center text-center flex-grow">
                  <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-16 h-16 mb-4 shrink-0">
                    <Image
                      // Use brand.logo directly as it's now logo_url from the service
                      src={brand.logo || getFaviconUrl(brand.domain)}
                      alt={brand.name}
                      width={64}
                      height={64}
                      className="brand-logo object-contain" // Added object-contain
                    />
                  </div>
                  <div className="flex flex-col flex-grow">
                    <h3 className="font-bold text-lg">{brand.name}</h3>
                    <div className="flex items-center justify-center mt-2 space-x-2">
                      <Badge variant="outline" className="text-xs">
                        {brand.category || "Uncategorized"}
                      </Badge>
                    </div>
                    <div className="mt-auto pt-2 flex items-center justify-center text-text-secondary">
                      <Flower className="h-4 w-4 mr-1" />
                      {/* flawaCount is now directly from the service */}
                      <span>{formatCount(brand.flawaCount)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
        <div className="flex justify-center mt-10">
          <Link href="/brands">
            <Button className="bg-black text-white hover:bg-gray-800">
              View All Brands
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
