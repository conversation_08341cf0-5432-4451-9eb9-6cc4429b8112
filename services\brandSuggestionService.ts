import { supabase } from '@/lib/supabaseClient';

/**
 * Fetches the count of brand suggestions with 'pending' status.
 * Assumes a 'status' column in the 'brand_suggestions' table.
 * @returns A promise that resolves to the number of pending brand suggestions.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchPendingBrandSuggestionsCount = async (): Promise<number> => {
  const { count, error } = await supabase
    .from('brand_suggestions')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'pending');

  if (error) {
    console.error('Error fetching pending brand suggestions count:', error);
    throw new Error(error.message);
  }
  return count ?? 0;
};

/**
 * Fetches the count of brand suggestions reviewed (approved or rejected) today.
 * Uses the 'updated_at' timestamp column and checks for 'approved' or 'rejected' status
 * in the 'brand_suggestions' table.
 * @param date The current date.
 * @returns A promise that resolves to the number of brand suggestions reviewed today.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchBrandSuggestionsReviewedTodayCount = async (date: Date): Promise<number> => {
  const startOfToday = new Date(date);
  startOfToday.setHours(0, 0, 0, 0);
  const endOfToday = new Date(date);
  endOfToday.setHours(23, 59, 59, 999);

  const { count, error } = await supabase
    .from('brand_suggestions')
    .select('*', { count: 'exact', head: true })
    .in('status', ['approved', 'rejected'])
    .gte('updated_at', startOfToday.toISOString())
    .lte('updated_at', endOfToday.toISOString());

  if (error) {
    console.error('Error fetching brand suggestions reviewed today count:', error);
    throw new Error(error.message);
  }
  return count ?? 0;
};

// Interface for BrandSuggestion data (can be expanded)
export interface BrandSuggestion {
    id: string;
    brand_name: string;
    brand_website?: string;
    reason?: string;
    suggested_by_user_id?: string;
    status: 'pending' | 'approved' | 'rejected';
    created_at: string;
    updated_at: string;
    // Add other relevant fields
}

/**
 * Fetches all brand suggestions.
 * @returns A promise that resolves to an array of BrandSuggestion objects.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchAllBrandSuggestions = async (): Promise<BrandSuggestion[]> => {
    const { data, error } = await supabase
        .from('brand_suggestions')
        .select('*')
        .order('created_at', { ascending: false });

    if (error) {
        console.error('Error fetching all brand suggestions:', error);
        throw new Error(error.message);
    }
    return data || [];
};

/**
 * Updates the status of a brand suggestion.
 * @param suggestionId The ID of the suggestion to update.
 * @param status The new status ('approved' or 'rejected').
 * @returns A promise that resolves to the updated BrandSuggestion object.
 * @throws Throws an error if the update operation fails.
 */
export const updateBrandSuggestionStatus = async (suggestionId: string, status: 'approved' | 'rejected'): Promise<BrandSuggestion | null> => {
    const { data, error } = await supabase
        .from('brand_suggestions')
        .update({ status: status, updated_at: new Date().toISOString() })
        .eq('id', suggestionId)
        .select()
        .single();

    if (error) {
        console.error(`Error updating brand suggestion ${suggestionId} to ${status}:`, error);
        throw new Error(error.message);
    }
    return data;
};