# 🗄️ DATABASE SCHEMA GAPS & REQUIREMENTS

## Missing Core Tables

### 1. **brands** table
```sql
CREATE TABLE brands (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  logo_url TEXT,
  domain VARCHAR(255) UNIQUE NOT NULL,
  description TEXT,
  website_url TEXT,
  category VARCHAR(100),
  is_claimed BOOLEAN DEFAULT false,
  claimed_by_user_id UUID REFERENCES auth.users(id),
  tier VARCHAR(50) DEFAULT 'Free', -- Free, Pro, Enterprise
  status VARCHAR(50) DEFAULT 'pending', -- pending, approved, rejected
  is_featured B<PERSON><PERSON>EAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. **flawas** table
```sql
CREATE TABLE flawas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type VARCHAR(50) NOT NULL, -- text, image, video, tweet
  content TEXT NOT NULL,
  author VA<PERSON>HAR(255) NOT NULL,
  author_image_url TEXT,
  brand_id UUID REFERENCES brands(id) ON DELETE CASCADE,
  likes INTEGER DEFAULT 0,
  views INTEGER DEFAULT 0,
  source_url TEXT,
  image_url TEXT,
  submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  social_links JSONB,
  user_id UUID REFERENCES auth.users(id),
  status VARCHAR(50) DEFAULT 'pending', -- pending, approved, rejected
  tags TEXT[],
  is_featured BOOLEAN DEFAULT false,
  moderation_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. **users** table (extends Supabase auth.users)
```sql
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email VARCHAR(255) NOT NULL,
  name VARCHAR(255),
  avatar_url TEXT,
  role VARCHAR(50) DEFAULT 'user', -- user, pageAdmin, sitewideAdmin
  managed_brand_ids UUID[],
  signup_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 4. **brand_suggestions** table
```sql
CREATE TABLE brand_suggestions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  suggested_name VARCHAR(255) NOT NULL,
  suggested_domain VARCHAR(255),
  suggested_website TEXT,
  suggested_category VARCHAR(100),
  suggested_description TEXT,
  suggester_email VARCHAR(255),
  suggester_name VARCHAR(255),
  status VARCHAR(50) DEFAULT 'pending', -- pending, approved, rejected
  admin_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  reviewed_by UUID REFERENCES auth.users(id)
);
```

### 5. **brand_claims** table
```sql
CREATE TABLE brand_claims (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  brand_id UUID REFERENCES brands(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  claim_status VARCHAR(50) DEFAULT 'pending_email_verification',
  verification_method VARCHAR(50), -- email, fallback_manual, request_new_brand
  verification_token VARCHAR(255),
  verification_expires_at TIMESTAMP WITH TIME ZONE,
  admin_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Missing Indexes
```sql
-- Performance indexes
CREATE INDEX idx_brands_domain ON brands(domain);
CREATE INDEX idx_brands_is_claimed ON brands(is_claimed);
CREATE INDEX idx_brands_is_featured ON brands(is_featured);
CREATE INDEX idx_flawas_brand_id ON flawas(brand_id);
CREATE INDEX idx_flawas_status ON flawas(status);
CREATE INDEX idx_flawas_submitted_at ON flawas(submitted_at DESC);
CREATE INDEX idx_flawas_likes ON flawas(likes DESC);
CREATE INDEX idx_flawas_views ON flawas(views DESC);
CREATE INDEX idx_user_profiles_role ON user_profiles(role);
```

## Missing Row Level Security (RLS)
```sql
-- Enable RLS
ALTER TABLE brands ENABLE ROW LEVEL SECURITY;
ALTER TABLE flawas ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE brand_suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE brand_claims ENABLE ROW LEVEL SECURITY;

-- RLS Policies needed for each table
-- (Detailed policies would be defined based on access requirements)
```
