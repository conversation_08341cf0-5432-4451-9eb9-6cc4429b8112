'use client';

import { useEffect, useState } from 'react';
import { Bar, <PERSON>hart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2 } from 'lucide-react';

interface TrendData {
  date: string;
  count: number;
}

interface ApiResponse {
  trends: TrendData[];
  message?: string;
  error?: string;
}

export function UserAcquisitionChart() {
  const [data, setData] = useState<TrendData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      setError(null);
      setMessage(null);
      try {
        const response = await fetch('/api/admin/user-acquisition');
        const result: ApiResponse = await response.json();

        if (!response.ok) {
          throw new Error(result.error || `API request failed with status ${response.status}`);
        }

        if (result.message) {
          setMessage(result.message);
        }
        setData(result.trends || []);

      } catch (e: any) {
        console.error("Failed to fetch user acquisition data:", e);
        setError(e.message || 'An unknown error occurred.');
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>User Acquisition Trends</CardTitle>
          <CardDescription>Signups over time.</CardDescription>
        </CardHeader>
        <CardContent className="h-[350px] flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <p className="ml-2">Loading trend data...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>User Acquisition Trends</CardTitle>
          <CardDescription>Signups over time.</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (message && data.length === 0) {
     return (
      <Card>
        <CardHeader>
          <CardTitle>User Acquisition Trends</CardTitle>
          <CardDescription>Signups over time.</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTitle>Information</AlertTitle>
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>User Acquisition Trends</CardTitle>
        <CardDescription>Daily user signups.</CardDescription>
      </CardHeader>
      <CardContent>
        {data.length > 0 ? (
          <div className="h-[350px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data} margin={{ top: 5, right: 20, left: 0, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                <YAxis stroke="#888888" fontSize={12} tickLine={false} axisLine={false} allowDecimals={false} />
                <Tooltip
                  contentStyle={{ backgroundColor: "hsl(var(--background))", border: "1px solid hsl(var(--border))" }}
                  labelStyle={{ color: "hsl(var(--foreground))" }}
                />
                <Legend wrapperStyle={{ paddingTop: '10px' }}/>
                <Bar dataKey="count" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} name="Signups" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        ) : (
          <p>No user acquisition data available to display.</p>
        )}
      </CardContent>
    </Card>
  );
}