"use client"

import Link from "next/link"
import { useState, useEffect } from "react"
import { Flower, ChevronDown, LogIn, LogOut, UserCircle, UserPlus } from "lucide-react" // Added icons
import { Button } from "@/components/ui/button"
import { useAuth } from "@/components/auth-provider" // Added useAuth
import AuthForm from "@/components/auth-form" // Added AuthForm
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { SubmissionForm } from "@/components/submission-form"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useIsMobile } from "@/hooks/use-mobile"

interface HeaderProps {
  brand?: {
    id: string
    name: string
    [key: string]: any
  }
}

export function Header({ brand }: HeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false)
  const isMobile = useIsMobile()
  const { user, signOut, isLoading } = useAuth() // Auth context
  const [authModalOpen, setAuthModalOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    // Set initial scroll state
    handleScroll()

    // Add event listener
    window.addEventListener("scroll", handleScroll)

    // Clean up event listener on component unmount
    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [])

  return (
    <header
      className={`w-full transition-all duration-300 ${isScrolled ? "bg-background/95 backdrop-blur-sm border-b" : ""}`}
    >
      <div className="container flex h-16 items-center justify-between px-4 md:px-6">
        <div className="flex items-center gap-2">
          <Link href="/" className="flex items-center space-x-2">
            <Flower className="h-5 w-5 md:h-6 md:w-6" />
            <span className="text-xl md:text-2xl font-bold">Flawagram</span>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center gap-3">
          {brand?.isClaimed && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-1">
                  <span>My Brand</span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href="/dashboard">Dashboard</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard?tab=analytics">Analytics</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard?tab=settings">Settings</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href="/create-brand" passHref>
                  <Button variant="outline" className="flex items-center gap-1 border-2">
                    <Flower className="h-4 w-4" />
                  </Button>
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <p>Create a new Flawagram Brand Page</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <Dialog>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2 bg-black text-white hover:bg-gray-800 border border-black">
                <Flower className="h-4 w-4" />
                <span>Send a Flawa</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Send a Flawa</DialogTitle>
                <DialogDescription>Share your love for a brand. {user ? `Logged in as ${user.email}` : "No account required."}</DialogDescription>
              </DialogHeader>
              <SubmissionForm inDialog={true} selectedBrandId={brand?.id} />
            </DialogContent>
          </Dialog>

          {!isLoading && user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full">
                  <UserCircle className="h-6 w-6" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem disabled>{user.email}</DropdownMenuItem>
                <DropdownMenuItem onClick={signOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log Out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : !isLoading && (
            <Dialog open={authModalOpen} onOpenChange={setAuthModalOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <LogIn className="mr-2 h-4 w-4" />
                  Login / Sign Up
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <AuthForm onAuthSuccess={() => setAuthModalOpen(false)} />
              </DialogContent>
            </Dialog>
          )}
        </div>

        {/* Mobile Buttons */}
        <div className="flex md:hidden items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href="/create-brand" passHref>
                  <Button variant="outline" size="icon" className="border-2">
                    <Flower className="h-5 w-5" />
                  </Button>
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <p>Create a new Flawagram Brand Page</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <Dialog>
            <DialogTrigger asChild>
              <Button size="icon" className="bg-black text-white hover:bg-gray-800 border border-black">
                <Flower className="h-5 w-5" />
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Send a Flawa</DialogTitle>
                <DialogDescription>Share your love for a brand. {user ? `Logged in as ${user.email}` : "No account required."}</DialogDescription>
              </DialogHeader>
              <SubmissionForm inDialog={true} selectedBrandId={brand?.id} />
            </DialogContent>
          </Dialog>
          {!isLoading && user ? (
             <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full">
                  <UserCircle className="h-6 w-6" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem disabled>{user.email}</DropdownMenuItem>
                <DropdownMenuItem onClick={signOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log Out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : !isLoading && (
            <Dialog open={authModalOpen} onOpenChange={setAuthModalOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="icon">
                  <UserPlus className="h-5 w-5" />
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                 <AuthForm onAuthSuccess={() => setAuthModalOpen(false)} />
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>
    </header>
  )
}
