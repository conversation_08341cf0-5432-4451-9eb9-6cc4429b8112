import { Brand } from "@/types"

export const brands: Record<string, Brand> = {
  apple: {
    id: "apple",
    name: "Apple",
    logo: null,
    domain: "apple.com",
    description:
      "Apple Inc. is an American multinational technology company that designs, develops, and sells consumer electronics, computer software, and online services.",
    website: "https://apple.com",
    category: "Technology",
    flawaCount: 1243,
    isClaimed: true,
    tier: "Pro",
  },
  nike: {
    id: "nike",
    name: "Nike",
    logo: null,
    domain: "nike.com",
    description:
      "Nike, Inc. is an American multinational corporation that designs, develops, manufactures, and markets footwear, apparel, equipment, accessories, and services.",
    website: "https://nike.com",
    category: "Fashion",
    flawaCount: 987,
    isClaimed: true,
    tier: "Pro",
  },
  spotify: {
    id: "spotify",
    name: "Spotify",
    logo: null,
    domain: "spotify.com",
    description:
      "Spotify is a digital music, podcast, and video streaming service that gives you access to millions of songs and other content from artists all over the world.",
    website: "https://spotify.com",
    category: "Entertainment",
    flawaCount: 756,
    isClaimed: false,
    tier: "Free",
  },
  tesla: {
    id: "tesla",
    name: "Tesla",
    logo: null,
    domain: "tesla.com",
    description:
      "Tesla, Inc. is an American electric vehicle and clean energy company that designs and manufactures electric cars, battery energy storage, solar panels, and related products and services.",
    website: "https://tesla.com",
    category: "Automotive",
    flawaCount: 642,
    isClaimed: true,
    tier: "Pro",
  },
  airbnb: {
    id: "airbnb",
    name: "Airbnb",
    logo: null,
    domain: "airbnb.com",
    description:
      "Airbnb, Inc. is an American company that operates an online marketplace for lodging, primarily homestays for vacation rentals, and tourism activities.",
    website: "https://airbnb.com",
    category: "Travel",
    flawaCount: 531,
    isClaimed: false,
    tier: "Free",
  },
  starbucks: {
    id: "starbucks",
    name: "Starbucks",
    logo: null,
    domain: "starbucks.com",
    description:
      "Starbucks Corporation is an American multinational chain of coffeehouses and roastery reserves headquartered in Seattle, Washington.",
    website: "https://starbucks.com",
    category: "Food & Beverage",
    flawaCount: 428,
    isClaimed: true,
    tier: "Pro",
  },
  amazon: {
    id: "amazon",
    name: "Amazon",
    logo: null,
    domain: "amazon.com",
    description:
      "Amazon.com, Inc. is an American multinational technology company focusing on e-commerce, cloud computing, artificial intelligence, and digital streaming.",
    website: "https://amazon.com",
    category: "E-commerce",
    flawaCount: 2156,
    isClaimed: true,
    tier: "Pro",
  },
  google: {
    id: "google",
    name: "Google",
    logo: null,
    domain: "google.com",
    description:
      "Google LLC is an American multinational technology company focusing on search engine technology, online advertising, cloud computing, computer software, quantum computing, e-commerce, and artificial intelligence.",
    website: "https://google.com",
    category: "Technology",
    flawaCount: 1876,
    isClaimed: true,
    tier: "Pro",
  },
}

export const featuredBrandIds = [
  "apple",
  "nike",
  "spotify",
  "tesla",
  "airbnb",
  "starbucks",
  "amazon",
  "google",
]

export const getFeaturedBrands = (): Brand[] => {
  return featuredBrandIds.map(id => brands[id])
} 