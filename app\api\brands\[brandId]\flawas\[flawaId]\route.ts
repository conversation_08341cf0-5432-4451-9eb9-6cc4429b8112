import { NextRequest, NextResponse } from 'next/server';
import { updateFlawaStatus, deleteFlawa, FlawaStatus } from '@/services/flawaService';
import { withBrandAuth, checkAdminAuth, checkBrandOwnership } from '@/lib/authUtils';

interface RouteParams {
  params: {
    brandId: string;
    flawaId: string;
  };
}

// Custom auth function for flawa operations - allows both brand owners and sitewide admins
async function checkFlawaAccess(brandId: string): Promise<boolean> {
  // Check if user is sitewide admin
  const isAdmin = await checkAdminAuth();
  if (isAdmin) return true;

  // Check if user owns the brand
  const ownsBrand = await checkBrandOwnership(brandId);
  return ownsBrand;
}

async function patchHandler(request: NextRequest, { params }: RouteParams) {
  try {
    const { brandId, flawaId } = params;

    if (!brandId || !flawaId) {
      return NextResponse.json({ error: 'Brand ID and Flawa ID are required' }, { status: 400 });
    }

    // Check access
    const hasAccess = await checkFlawaAccess(brandId);
    if (!hasAccess) {
      return NextResponse.json({ error: 'Unauthorized: Brand access required' }, { status: 403 });
    }

    const body = await request.json();
    const { status } = body;

    if (!status || !['pending', 'approved', 'rejected'].includes(status)) {
      return NextResponse.json({ error: 'Invalid status provided. Must be "pending", "approved", or "rejected".' }, { status: 400 });
    }

    const updatedFlawa = await updateFlawaStatus(flawaId, status as FlawaStatus);

    if (!updatedFlawa) {
      return NextResponse.json({ error: 'Flawa not found or failed to update' }, { status: 404 });
    }

    return NextResponse.json(updatedFlawa);

  } catch (error) {
    console.error('Error in PATCH /api/brands/[brandId]/flawas/[flawaId]:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to update flawa', details: errorMessage }, { status: 500 });
  }
}

async function deleteHandler(request: NextRequest, { params }: RouteParams) {
  try {
    const { brandId, flawaId } = params;

    if (!brandId || !flawaId) {
      return NextResponse.json({ error: 'Brand ID and Flawa ID are required' }, { status: 400 });
    }

    // Check access
    const hasAccess = await checkFlawaAccess(brandId);
    if (!hasAccess) {
      return NextResponse.json({ error: 'Unauthorized: Brand access required' }, { status: 403 });
    }

    await deleteFlawa(flawaId);
    return NextResponse.json({ message: 'Flawa deleted successfully' }, { status: 200 });

  } catch (error) {
    console.error('Error in DELETE /api/brands/[brandId]/flawas/[flawaId]:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to delete flawa', details: errorMessage }, { status: 500 });
  }
}

export const PATCH = patchHandler;
export const DELETE = deleteHandler;
