import { NextRequest, NextResponse } from 'next/server';
import { fetchBrandByDomain } from '@/services/brandService';

interface RouteParams {
  params: {
    domain: string;
  };
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { domain } = params;

    if (!domain) {
      return NextResponse.json({ error: 'Domain is required' }, { status: 400 });
    }

    const brand = await fetchBrandByDomain(domain);

    if (!brand) {
      return NextResponse.json({ error: 'Brand not found' }, { status: 404 });
    }

    return NextResponse.json(brand);

  } catch (error) {
    console.error('Error in GET /api/brands/by-domain/[domain]:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to fetch brand', details: errorMessage }, { status: 500 });
  }
}
