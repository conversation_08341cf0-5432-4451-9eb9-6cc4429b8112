"use client"

import { Suspense, useState, useEffect } from "react" // Added Suspense and useEffect
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"; // Added Skeleton

// Import analytics components
import { UserAcquisitionChart } from '@/components/admin/user-acquisition-chart';
import { FunnelAnalysisCharts } from '@/components/admin/funnel-analysis-charts';
import { SearchAnalyticsDisplay } from '@/components/admin/search-analytics-display';
import { ModerationMetricsDisplay } from '@/components/admin/moderation-metrics-display';
import { CSVUpload } from "@/components/admin/csv-upload";
import {
  Bell,
  Building,
  ChevronDown,
  Download,
  Eye,
  Flower,
  MoreHorizontal,
  Search,
  Settings,
  Star,
  Trash,
  CheckCircle,
  XCircle,
  MessageSquarePlus, // Icon for suggestions
  Upload, // Icon for bulk operations
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import Image from "next/image"
import { Brand, Flawa } from "@/types"; // Assuming Brand and Flawa types are defined here
import { BrandSuggestion } from "@/services/brandSuggestionService"; // Import type
import { FlawaStatus } from "@/services/flawaService"; // Import FlawaStatus type
import { BrandAdminStatus } from "@/services/brandService"; // Import BrandAdminStatus type


// Define a more specific type for recent brands if needed, including status
interface RecentBrand extends Brand {
  status?: BrandAdminStatus | string; // Make status optional or ensure it's always there, use BrandAdminStatus
  isFeatured?: boolean;
}

// Flawa type from '@/types' should be sufficient, but ensure it matches API response
// interface RecentFlawa extends Flawa {
//   // any additional admin-specific fields if necessary
// }


export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState("overview")

  const initialStats = {
    totalBrands: 0,
    pendingBrands: 0,
    totalFlawas: 0,
    pendingFlawas: 0,
    brandsChange: 0,
    flawasChange: 0,
    flawasModeratedToday: 0,
    suggestionsReviewedToday: 0,
    pendingBrandSuggestions: 0,
  };

  const [dashboardStats, setDashboardStats] = useState(initialStats);
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  const [recentBrandsData, setRecentBrandsData] = useState<RecentBrand[]>([]);
  const [isLoadingRecentBrands, setIsLoadingRecentBrands] = useState(true);
  const [recentBrandsError, setRecentBrandsError] = useState<string | null>(null);

  const [recentFlawasData, setRecentFlawasData] = useState<Flawa[]>([]);
  const [isLoadingRecentFlawas, setIsLoadingRecentFlawas] = useState(true);
  const [recentFlawasError, setRecentFlawasError] = useState<string | null>(null);

  const [brandSuggestionsData, setBrandSuggestionsData] = useState<BrandSuggestion[]>([]);
  const [isLoadingBrandSuggestions, setIsLoadingBrandSuggestions] = useState(true);
  const [brandSuggestionsError, setBrandSuggestionsError] = useState<string | null>(null);


  useEffect(() => {
    const fetchStats = async () => {
      setIsLoadingStats(true);
      setStatsError(null);
      try {
        const response = await fetch('/api/admin/stats');
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to fetch stats: ${response.statusText}`);
        }
        const data = await response.json();
        setDashboardStats(data);
      } catch (error) {
        console.error("Error fetching admin stats:", error);
        setStatsError(error instanceof Error ? error.message : "An unknown error occurred");
        setDashboardStats(initialStats); // Reset to initial on error
      } finally {
        setIsLoadingStats(false);
      }
    };

    const fetchRecentBrands = async () => {
      setIsLoadingRecentBrands(true);
      setRecentBrandsError(null);
      try {
        const response = await fetch('/api/admin/recent-brands?limit=5');
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to fetch recent brands: ${response.statusText}`);
        }
        const data: RecentBrand[] = await response.json();
        setRecentBrandsData(data);
      } catch (error) {
        console.error("Error fetching recent brands:", error);
        setRecentBrandsError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoadingRecentBrands(false);
      }
    };

    const fetchRecentFlawas = async () => {
      setIsLoadingRecentFlawas(true);
      setRecentFlawasError(null);
      try {
        const response = await fetch('/api/admin/recent-flawas?limit=5');
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to fetch recent flawas: ${response.statusText}`);
        }
        const data: Flawa[] = await response.json();
        setRecentFlawasData(data);
      } catch (error) {
        console.error("Error fetching recent flawas:", error);
        setRecentFlawasError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoadingRecentFlawas(false);
      }
    };

    fetchStats();
    fetchRecentBrands();
    fetchRecentFlawas();

    const fetchBrandSuggestions = async () => {
      setIsLoadingBrandSuggestions(true);
      setBrandSuggestionsError(null);
      try {
        const response = await fetch('/api/admin/brand-suggestions');
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to fetch brand suggestions: ${response.statusText}`);
        }
        const data: BrandSuggestion[] = await response.json();
        setBrandSuggestionsData(data);
      } catch (error) {
        console.error("Error fetching brand suggestions:", error);
        setBrandSuggestionsError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoadingBrandSuggestions(false);
      }
    };
    fetchBrandSuggestions();
  }, []);

  const handleSuggestionAction = async (suggestionId: string, newStatus: 'approved' | 'rejected') => {
    // Optimistically update UI or show loading state on the specific item
    const originalSuggestions = [...brandSuggestionsData];
    setBrandSuggestionsData(prev => prev.map(s => s.id === suggestionId ? { ...s, status: newStatus } : s));

    try {
      const response = await fetch(`/api/admin/brand-suggestions/${suggestionId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update suggestion: ${response.statusText}`);
      }
      const updatedSuggestion: BrandSuggestion = await response.json();
      // Update with server response to ensure consistency
      setBrandSuggestionsData(prev => prev.map(s => s.id === updatedSuggestion.id ? updatedSuggestion : s));
      // Refresh stats to update pending count badge
      // Consider a more targeted update for dashboardStats.pendingBrandSuggestions if possible
      const statsResponse = await fetch('/api/admin/stats');
      const statsData = await statsResponse.json();
      setDashboardStats(statsData);

    } catch (error) {
      console.error(`Error ${newStatus === 'approved' ? 'approving' : 'rejecting'} suggestion:`, error);
      // Revert optimistic update on error
      setBrandSuggestionsData(originalSuggestions);
      // Show toast or error message to user
      alert(`Failed to ${newStatus} suggestion: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  const refreshDashboardData = async (dataType?: 'all' | 'stats' | 'brands' | 'flawas' | 'suggestions') => {
    if (dataType === 'all' || dataType === 'stats' || !dataType) {
      setIsLoadingStats(true);
    }
    if (dataType === 'all' || dataType === 'brands' || !dataType) {
      setIsLoadingRecentBrands(true);
    }
    if (dataType === 'all' || dataType === 'flawas' || !dataType) {
      setIsLoadingRecentFlawas(true);
    }
    if (dataType === 'all' || dataType === 'suggestions' || !dataType) {
      setIsLoadingBrandSuggestions(true);
    }

    try {
      const promises = [];
      if (dataType === 'all' || dataType === 'stats' || !dataType) {
        promises.push(fetch('/api/admin/stats'));
      }
      if (dataType === 'all' || dataType === 'brands' || !dataType) {
        promises.push(fetch('/api/admin/recent-brands?limit=5'));
      }
      if (dataType === 'all' || dataType === 'flawas' || !dataType) {
        promises.push(fetch('/api/admin/recent-flawas?limit=5'));
      }
      if (dataType === 'all' || dataType === 'suggestions' || !dataType) {
        promises.push(fetch('/api/admin/brand-suggestions'));
      }

      const responses = await Promise.all(promises);
      let responseIndex = 0;

      if (dataType === 'all' || dataType === 'stats' || !dataType) {
        const statsResponse = responses[responseIndex++];
        if (statsResponse.ok) setDashboardStats(await statsResponse.json());
        else console.error("Failed to refresh stats");
      }
      if (dataType === 'all' || dataType === 'brands' || !dataType) {
        const brandsResponse = responses[responseIndex++];
        if (brandsResponse.ok) setRecentBrandsData(await brandsResponse.json());
        else console.error("Failed to refresh brands");
      }
      if (dataType === 'all' || dataType === 'flawas' || !dataType) {
        const flawasResponse = responses[responseIndex++];
        if (flawasResponse.ok) setRecentFlawasData(await flawasResponse.json());
        else console.error("Failed to refresh flawas");
      }
      if (dataType === 'all' || dataType === 'suggestions' || !dataType) {
        const suggestionsResponse = responses[responseIndex++];
        if (suggestionsResponse.ok) setBrandSuggestionsData(await suggestionsResponse.json());
        else console.error("Failed to refresh suggestions");
      }

    } catch (error) {
      console.error("Error refreshing dashboard data:", error);
    } finally {
      if (dataType === 'all' || dataType === 'stats' || !dataType) setIsLoadingStats(false);
      if (dataType === 'all' || dataType === 'brands' || !dataType) setIsLoadingRecentBrands(false);
      if (dataType === 'all' || dataType === 'flawas' || !dataType) setIsLoadingRecentFlawas(false);
      if (dataType === 'all' || dataType === 'suggestions' || !dataType) setIsLoadingBrandSuggestions(false);
    }
  };


  const handleFlawaStatusUpdate = async (flawaId: string, newStatus: FlawaStatus) => {
    const originalFlawas = [...recentFlawasData];
    setRecentFlawasData(prev => prev.map(f => f.id === flawaId ? { ...f, status: newStatus } : f));

    try {
      const response = await fetch(`/api/admin/flawas/${flawaId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update Flawa status: ${response.statusText}`);
      }
      // const updatedFlawa: Flawa = await response.json();
      // setRecentFlawasData(prev => prev.map(f => f.id === updatedFlawa.id ? updatedFlawa : f));
      // Instead of just updating one, refresh all data to ensure consistency and stats update
      await refreshDashboardData();
      alert(`Flawa status updated to ${newStatus}.`);


    } catch (error) {
      console.error(`Error updating Flawa status for ${flawaId}:`, error);
      setRecentFlawasData(originalFlawas); // Revert optimistic update
      alert(`Failed to update Flawa status: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  const handleFlawaDelete = async (flawaId: string) => {
    if (!confirm("Are you sure you want to delete this Flawa? This action cannot be undone.")) {
      return;
    }
    const originalFlawas = [...recentFlawasData];
    setRecentFlawasData(prev => prev.filter(f => f.id !== flawaId));

    try {
      const response = await fetch(`/api/admin/flawas/${flawaId}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to delete Flawa: ${response.statusText}`);
      }
      // Data already filtered optimistically. Refresh stats.
      await refreshDashboardData(); // Refresh stats and list
      alert("Flawa deleted successfully.");

    } catch (error) {
      console.error(`Error deleting Flawa ${flawaId}:`, error);
      setRecentFlawasData(originalFlawas); // Revert optimistic update
      alert(`Failed to delete Flawa: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  const handleBrandStatusUpdate = async (brandId: string, newStatus: BrandAdminStatus) => {
    const originalBrands = [...recentBrandsData];
    setRecentBrandsData(prev => prev.map(b => b.id === brandId ? { ...b, status: newStatus } : b));

    try {
      const response = await fetch(`/api/admin/brands/${brandId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update Brand status: ${response.statusText}`);
      }
      await refreshDashboardData('all'); // Refresh all data including stats
      alert(`Brand status updated to ${newStatus}.`);
    } catch (error) {
      console.error(`Error updating Brand status for ${brandId}:`, error);
      setRecentBrandsData(originalBrands);
      alert(`Failed to update Brand status: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  const handleBrandFeatureToggle = async (brandId: string, currentIsFeatured?: boolean) => {
    const newIsFeatured = !currentIsFeatured;
    const originalBrands = [...recentBrandsData];
    setRecentBrandsData(prev => prev.map(b => b.id === brandId ? { ...b, isFeatured: newIsFeatured } : b));

    try {
      const response = await fetch(`/api/admin/brands/${brandId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isFeatured: newIsFeatured }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update Brand feature status: ${response.statusText}`);
      }
      await refreshDashboardData('brands'); // Refresh only brands list
      alert(`Brand ${newIsFeatured ? 'featured' : 'unfeatured'} successfully.`);
    } catch (error) {
      console.error(`Error toggling Brand feature status for ${brandId}:`, error);
      setRecentBrandsData(originalBrands);
      alert(`Failed to toggle Brand feature status: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  const handleBrandDelete = async (brandId: string) => {
    if (!confirm("Are you sure you want to delete this Brand? This may also delete associated Flawas depending on database setup. This action cannot be undone.")) {
      return;
    }
    const originalBrands = [...recentBrandsData];
    setRecentBrandsData(prev => prev.filter(b => b.id !== brandId));

    try {
      const response = await fetch(`/api/admin/brands/${brandId}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to delete Brand: ${response.statusText}`);
      }
      await refreshDashboardData('all'); // Refresh all data
      alert("Brand deleted successfully.");
    } catch (error) {
      console.error(`Error deleting Brand ${brandId}:`, error);
      setRecentBrandsData(originalBrands);
      alert(`Failed to delete Brand: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  // Get favicon URL as fallback for brand logo
  const getFaviconUrl = (domain: string, size = 128): string => {
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
  }

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1 bg-muted/30">
        <div className="container px-4 md:px-6 py-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
            <div>
              <h1 className="text-2xl font-bold">Admin Dashboard</h1>
              <p className="text-text-secondary">Manage brands, flawas, and platform settings.</p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Bell className="h-4 w-4" />
                <span>Notifications</span>
              </Button>
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Settings className="h-4 w-4" />
                <span>Settings</span>
              </Button>
            </div>
          </div>

          <Tabs defaultValue="overview" className="space-y-4" onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 md:grid-cols-5 lg:grid-cols-8 w-full"> {/* Adjusted grid columns */}
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="brands">Brands</TabsTrigger>
              <TabsTrigger value="flawas">Flawas</TabsTrigger>
              <TabsTrigger value="suggestions">
                Suggestions <Badge variant="outline" className="ml-1.5">{isLoadingStats ? "..." : dashboardStats.pendingBrandSuggestions}</Badge>
              </TabsTrigger>
              <TabsTrigger value="bulk-ops" className="hidden sm:block">
                Bulk Ops
              </TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="users" className="hidden md:block">
                Users
              </TabsTrigger>
              <TabsTrigger value="settings" className="hidden lg:block">
                Settings
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6"> {/* Adjusted for more cards */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Brands</CardTitle>
                    <Building className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {isLoadingStats ? <Skeleton className="h-8 w-20" /> : dashboardStats.totalBrands}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {isLoadingStats ? <Skeleton className="h-4 w-24 mt-1" /> : `+${dashboardStats.brandsChange} new this month`}
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Pending Brands</CardTitle>
                    <Building className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {isLoadingStats ? <Skeleton className="h-8 w-16" /> : dashboardStats.pendingBrands}
                    </div>
                    <p className="text-xs text-muted-foreground">Awaiting approval</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Flawas</CardTitle>
                    <Flower className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {isLoadingStats ? <Skeleton className="h-8 w-24" /> : dashboardStats.totalFlawas.toLocaleString()}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {isLoadingStats ? <Skeleton className="h-4 w-24 mt-1" /> : `+${dashboardStats.flawasChange} new this month`}
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Pending Flawas</CardTitle>
                    <Flower className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {isLoadingStats ? <Skeleton className="h-8 w-16" /> : dashboardStats.pendingFlawas}
                    </div>
                    <p className="text-xs text-muted-foreground">Awaiting moderation</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Flawas Moderated (Today)</CardTitle>
                    <CheckCircle className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {isLoadingStats ? <Skeleton className="h-8 w-12" /> : dashboardStats.flawasModeratedToday}
                    </div>
                    <p className="text-xs text-muted-foreground">Manual reviews today</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Suggestions Reviewed (Today)</CardTitle>
                    <MessageSquarePlus className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {isLoadingStats ? <Skeleton className="h-8 w-12" /> : dashboardStats.suggestionsReviewedToday}
                    </div>
                    <p className="text-xs text-muted-foreground">Brand suggestions processed</p>
                  </CardContent>
                </Card>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Brands</CardTitle>
                    <CardDescription>Recently added or updated brands.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Brand</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Tier</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {isLoadingRecentBrands ? (
                          Array.from({ length: 5 }).map((_, index) => (
                            <TableRow key={`skeleton-brand-${index}`}>
                              <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                              <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                              <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                              <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                              <TableCell className="text-right"><Skeleton className="h-8 w-8 ml-auto" /></TableCell>
                            </TableRow>
                          ))
                        ) : recentBrandsError ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center text-red-500">
                              Error loading recent brands: {recentBrandsError}
                            </TableCell>
                          </TableRow>
                        ) : recentBrandsData.length === 0 ? (
                           <TableRow>
                            <TableCell colSpan={5} className="text-center text-muted-foreground">
                              No recent brands found.
                            </TableCell>
                          </TableRow>
                        ) : (
                          recentBrandsData.map((brand) => (
                            <TableRow key={brand.id}>
                              <TableCell className="font-medium">
                                <div className="flex items-center gap-2">
                                  <div className="w-6 h-6 rounded-full overflow-hidden bg-muted">
                                    <Image
                                      src={brand.logo || getFaviconUrl(brand.domain) || "/placeholder.svg"}
                                      alt={brand.name}
                                      width={24}
                                      height={24}
                                      className="object-cover"
                                      onError={(e) => (e.currentTarget.src = "/placeholder.svg")}
                                    />
                                  </div>
                                  <span>{brand.name}</span>
                                </div>
                              </TableCell>
                              <TableCell>{brand.category || "N/A"}</TableCell>
                              <TableCell>
                                <Badge variant={brand.status === "pending" ? "outline" : "default"}>
                                  {brand.status ? brand.status.charAt(0).toUpperCase() + brand.status.slice(1) : (brand.isClaimed ? "Claimed" : "Unclaimed")}
                                </Badge>
                              </TableCell>
                              <TableCell>{brand.tier || "N/A"}</TableCell>
                              <TableCell className="text-right">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem>
                                      <Eye className="mr-2 h-4 w-4" />
                                      <span>View</span>
                                    </DropdownMenuItem>
                                    {/* Add Approve/Reject if brand status is pending */}
                                    {brand.status === "pending" && (
                                      <>
                                        <DropdownMenuItem>
                                          <CheckCircle className="mr-2 h-4 w-4" />
                                          <span>Approve</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem>
                                          <XCircle className="mr-2 h-4 w-4" />
                                          <span>Reject</span>
                                        </DropdownMenuItem>
                                      </>
                                    )}
                                    <DropdownMenuItem>
                                      <Star className="mr-2 h-4 w-4" />
                                      <span>Feature</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem className="text-red-600 hover:!text-red-600">
                                      <Trash className="mr-2 h-4 w-4" />
                                      <span>Delete</span>
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Recent Flawas</CardTitle>
                    <CardDescription>Recently submitted flawas.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Content</TableHead>
                          <TableHead>Author</TableHead>
                          <TableHead>Brand</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {isLoadingRecentFlawas ? (
                          Array.from({ length: 5 }).map((_, index) => (
                            <TableRow key={`skeleton-flawa-${index}`}>
                              <TableCell><Skeleton className="h-6 w-48" /></TableCell>
                              <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                              <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                              <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                              <TableCell className="text-right"><Skeleton className="h-8 w-8 ml-auto" /></TableCell>
                            </TableRow>
                          ))
                        ) : recentFlawasError ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center text-red-500">
                              Error loading recent flawas: {recentFlawasError}
                            </TableCell>
                          </TableRow>
                        ) : recentFlawasData.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center text-muted-foreground">
                              No recent flawas found.
                            </TableCell>
                          </TableRow>
                        ) : (
                          recentFlawasData.map((flawa) => (
                            <TableRow key={flawa.id}>
                              <TableCell className="max-w-[200px] truncate" title={flawa.content}>{flawa.content}</TableCell>
                              <TableCell>{flawa.author || "N/A"}</TableCell>
                              <TableCell>{flawa.brand || "N/A"}</TableCell>
                              <TableCell>
                                <Badge variant={flawa.status === "pending" ? "destructive" : flawa.status === "approved" ? "default" : "secondary"}>
                                  {flawa.status ? flawa.status.charAt(0).toUpperCase() + flawa.status.slice(1) : "Unknown"}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={() => alert(`View details for Flawa ID: ${flawa.id}`)}>
                                      <Eye className="mr-2 h-4 w-4" />
                                      <span>View Details</span>
                                    </DropdownMenuItem>
                                    {flawa.status === "pending" && (
                                      <>
                                        <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'approved')}>
                                          <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                          <span>Approve</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'rejected')}>
                                          <XCircle className="mr-2 h-4 w-4 text-orange-500" />
                                          <span>Reject</span>
                                        </DropdownMenuItem>
                                      </>
                                    )}
                                    {flawa.status === "approved" && (
                                      <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'pending')}>
                                        <XCircle className="mr-2 h-4 w-4 text-yellow-500" />
                                        <span>Unapprove (Move to Pending)</span>
                                      </DropdownMenuItem>
                                    )}
                                     {flawa.status === "rejected" && (
                                      <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'pending')}>
                                        <CheckCircle className="mr-2 h-4 w-4 text-yellow-500" />
                                        <span>Move to Pending</span>
                                      </DropdownMenuItem>
                                    )}
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem className="text-red-600 hover:!text-red-600" onClick={() => handleFlawaDelete(flawa.id)}>
                                      <Trash className="mr-2 h-4 w-4" />
                                      <span>Delete</span>
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="brands" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>All Brands</CardTitle>
                  <CardDescription>Manage all brands on the platform.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <div className="relative">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input placeholder="Search brands..." className="pl-8 w-[250px]" />
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" className="flex items-center gap-1">
                            <span>Filter</span>
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>All</DropdownMenuItem>
                          <DropdownMenuItem>Claimed</DropdownMenuItem>
                          <DropdownMenuItem>Unclaimed</DropdownMenuItem>
                          <DropdownMenuItem>Pending</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <Button className="flex items-center gap-1">
                      <Download className="h-4 w-4" />
                      <span>Export</span>
                    </Button>
                  </div>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Brand</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Tier</TableHead>
                        <TableHead>Flawas</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoadingRecentBrands ? (
                          Array.from({ length: 5 }).map((_, index) => (
                            <TableRow key={`skeleton-all-brand-${index}`}>
                              <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                              <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                              <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                              <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                              <TableCell><Skeleton className="h-6 w-12" /></TableCell>
                              <TableCell className="text-right"><Skeleton className="h-8 w-8 ml-auto" /></TableCell>
                            </TableRow>
                          ))
                        ) : recentBrandsError ? (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center text-red-500">
                              Error loading brands: {recentBrandsError}
                            </TableCell>
                          </TableRow>
                        ) : recentBrandsData.length === 0 ? (
                           <TableRow>
                            <TableCell colSpan={6} className="text-center text-muted-foreground">
                              No brands found.
                            </TableCell>
                          </TableRow>
                        ) : (
                          recentBrandsData.map((brand: RecentBrand) => ( // Added type for brand
                            <TableRow key={brand.id}>
                              <TableCell className="font-medium">
                                <div className="flex items-center gap-2">
                                  <div className="w-6 h-6 rounded-full overflow-hidden bg-muted">
                                    <Image
                                      src={brand.logo || getFaviconUrl(brand.domain) || "/placeholder.svg"}
                                      alt={brand.name}
                                      width={24}
                                      height={24}
                                      className="object-cover"
                                      onError={(e) => (e.currentTarget.src = "/placeholder.svg")}
                                    />
                                  </div>
                                  <span>{brand.name}</span>
                                </div>
                              </TableCell>
                              <TableCell>{brand.category || "N/A"}</TableCell>
                              <TableCell>
                                <Badge variant={brand.status === "pending" ? "outline" : "default"}>
                                  {brand.status ? brand.status.charAt(0).toUpperCase() + brand.status.slice(1) : (brand.isClaimed ? "Claimed" : "Unclaimed")}
                                </Badge>
                              </TableCell>
                              <TableCell>{brand.tier || "N/A"}</TableCell>
                              <TableCell>{brand.flawaCount}</TableCell>
                              <TableCell className="text-right">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={() => alert(`View brand ${brand.name}`)}>
                                      <Eye className="mr-2 h-4 w-4" />
                                      <span>View</span>
                                    </DropdownMenuItem>
                                     {brand.status === "pending" && (
                                      <>
                                        <DropdownMenuItem onClick={() => handleBrandStatusUpdate(brand.id, 'approved')}>
                                          <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                          <span>Approve</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleBrandStatusUpdate(brand.id, 'rejected')}>
                                          <XCircle className="mr-2 h-4 w-4 text-orange-500" />
                                          <span>Reject</span>
                                        </DropdownMenuItem>
                                      </>
                                    )}
                                    {/* Allow featuring/unfeaturing regardless of pending status for "All Brands" table too */}
                                    <DropdownMenuItem onClick={() => handleBrandFeatureToggle(brand.id, brand.isFeatured)}>
                                      <Star className={`mr-2 h-4 w-4 ${brand.isFeatured ? "text-yellow-500 fill-yellow-500" : "text-gray-400"}`} />
                                      <span>{brand.isFeatured ? 'Unfeature' : 'Feature'}</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem className="text-red-600 hover:!text-red-600" onClick={() => handleBrandDelete(brand.id)}>
                                      <Trash className="mr-2 h-4 w-4" />
                                      <span>Delete</span>
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="flawas" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>All Flawas</CardTitle>
                  <CardDescription>Manage all flawas on the platform.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <div className="relative">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input placeholder="Search flawas..." className="pl-8 w-[250px]" />
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" className="flex items-center gap-1">
                            <span>Filter</span>
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>All</DropdownMenuItem>
                          <DropdownMenuItem>Approved</DropdownMenuItem>
                          <DropdownMenuItem>Pending</DropdownMenuItem>
                          <DropdownMenuItem>Rejected</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <Button className="flex items-center gap-1">
                      <Download className="h-4 w-4" />
                      <span>Export</span>
                    </Button>
                  </div>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Content</TableHead>
                        <TableHead>Author</TableHead>
                        <TableHead>Brand</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoadingRecentFlawas ? (
                          Array.from({ length: 10 }).map((_, index) => ( // Show more skeletons for "All Flawas"
                            <TableRow key={`skeleton-all-flawa-${index}`}>
                              <TableCell><Skeleton className="h-6 w-48" /></TableCell>
                              <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                              <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                              <TableCell><Skeleton className="h-6 w-20" /></TableCell> {/* Date Submitted */}
                              <TableCell><Skeleton className="h-6 w-20" /></TableCell> {/* Status */}
                              <TableCell className="text-right"><Skeleton className="h-8 w-8 ml-auto" /></TableCell>
                            </TableRow>
                          ))
                        ) : recentFlawasError ? (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center text-red-500">
                              Error loading flawas: {recentFlawasError}
                            </TableCell>
                          </TableRow>
                        ) : recentFlawasData.length === 0 ? (
                           <TableRow>
                            <TableCell colSpan={6} className="text-center text-muted-foreground">
                              No flawas found.
                            </TableCell>
                          </TableRow>
                        ) : (
                          recentFlawasData.map((flawa: Flawa) => ( // Use recentFlawasData and type flawa
                            <TableRow key={flawa.id}>
                              <TableCell className="max-w-[250px] truncate" title={flawa.content}>{flawa.content}</TableCell>
                              <TableCell>{flawa.author || "N/A"}</TableCell>
                              <TableCell>{flawa.brand || "N/A"}</TableCell>
                              <TableCell>{flawa.timestamp ? new Date(flawa.timestamp).toLocaleDateString() : "N/A"}</TableCell>
                              <TableCell>
                                <Badge variant={flawa.status === "pending" ? "destructive" : flawa.status === "approved" ? "default" : "secondary"}>
                                  {flawa.status ? flawa.status.charAt(0).toUpperCase() + flawa.status.slice(1) : "Unknown"}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={() => alert(`View details for Flawa ID: ${flawa.id}`)}>
                                      <Eye className="mr-2 h-4 w-4" />
                                      <span>View Details</span>
                                    </DropdownMenuItem>
                                    {flawa.status === "pending" && (
                                      <>
                                        <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'approved')}>
                                          <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                          <span>Approve</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'rejected')}>
                                          <XCircle className="mr-2 h-4 w-4 text-orange-500" />
                                          <span>Reject</span>
                                        </DropdownMenuItem>
                                      </>
                                    )}
                                     {flawa.status === "approved" && (
                                      <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'pending')}>
                                        <XCircle className="mr-2 h-4 w-4 text-yellow-500" />
                                        <span>Unapprove (Move to Pending)</span>
                                      </DropdownMenuItem>
                                    )}
                                    {flawa.status === "rejected" && (
                                      <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'pending')}>
                                        <CheckCircle className="mr-2 h-4 w-4 text-yellow-500" />
                                        <span>Move to Pending</span>
                                      </DropdownMenuItem>
                                    )}
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem className="text-red-600 hover:!text-red-600" onClick={() => handleFlawaDelete(flawa.id)}>
                                      <Trash className="mr-2 h-4 w-4" />
                                      <span>Delete</span>
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>
<TabsContent value="suggestions" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Brand Suggestions</CardTitle>
                  <CardDescription>Review and manage brand suggestions submitted by users.</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Brand Name</TableHead>
                        <TableHead>Website/Link</TableHead>
                        <TableHead>Reason</TableHead>
                        <TableHead>Suggested By User ID</TableHead>
                        <TableHead>Date Suggested</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoadingBrandSuggestions ? (
                        Array.from({ length: 5 }).map((_, index) => (
                          <TableRow key={`skeleton-suggestion-${index}`}>
                            <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                            <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                            <TableCell><Skeleton className="h-6 w-40" /></TableCell>
                            <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                            <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                            <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                            <TableCell className="text-right"><Skeleton className="h-8 w-8 ml-auto" /></TableCell>
                          </TableRow>
                        ))
                      ) : brandSuggestionsError ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center text-red-500">
                            Error loading brand suggestions: {brandSuggestionsError}
                          </TableCell>
                        </TableRow>
                      ) : brandSuggestionsData.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center text-muted-foreground">
                            No brand suggestions found.
                          </TableCell>
                        </TableRow>
                      ) : (
                        brandSuggestionsData.map((suggestion: BrandSuggestion) => (
                          <TableRow key={suggestion.id}>
                            <TableCell className="font-medium">{suggestion.brand_name}</TableCell>
                            <TableCell>
                              {suggestion.brand_website ? (
                                <a
                                  href={suggestion.brand_website}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:underline truncate max-w-[150px] block"
                                  title={suggestion.brand_website}
                                >
                                  {suggestion.brand_website}
                                </a>
                              ) : (
                                <span className="text-muted-foreground">N/A</span>
                              )}
                            </TableCell>
                            <TableCell className="max-w-[200px] truncate" title={suggestion.reason || undefined}>{suggestion.reason || "N/A"}</TableCell>
                            <TableCell>{suggestion.suggested_by_user_id || "Anonymous"}</TableCell>
                            <TableCell>{new Date(suggestion.created_at).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  suggestion.status === "pending"
                                    ? "destructive" // Using destructive for pending to make it stand out
                                    : suggestion.status === "approved"
                                      ? "default" // Default (often green or primary) for approved
                                      : "secondary" // Secondary for rejected or other statuses
                                }
                                className={
                                  suggestion.status === "approved" ? "bg-green-100 text-green-700 border-green-300"
                                  : suggestion.status === "rejected" ? "bg-orange-100 text-orange-700 border-orange-300" // Example for rejected
                                  : "" // Default for pending (destructive will handle its styling)
                                }
                              >
                                {suggestion.status.charAt(0).toUpperCase() + suggestion.status.slice(1)}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              {suggestion.status === "pending" && (
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={() => handleSuggestionAction(suggestion.id, 'approved')}>
                                      <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                      <span>Approve</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleSuggestionAction(suggestion.id, 'rejected')}>
                                      <XCircle className="mr-2 h-4 w-4 text-red-500" />
                                      <span>Reject</span>
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              )}
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="bulk-ops" className="space-y-4">
              <CSVUpload />
            </TabsContent>

            {/* Placeholder for Analytics, Users, Settings tabs */}
            <TabsContent value="analytics" className="space-y-6">
              {/* User Acquisition Trends */}
              <Suspense fallback={<Skeleton className="h-[450px] w-full" />}>
                <UserAcquisitionChart />
              </Suspense>

              {/* Funnel Analysis */}
              <Suspense fallback={<Skeleton className="h-[500px] w-full" />}>
                <FunnelAnalysisCharts />
              </Suspense>

              {/* Search Analytics */}
              <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
                <SearchAnalyticsDisplay />
              </Suspense>

              {/* Moderation Metrics */}
              <Suspense fallback={<Skeleton className="h-[450px] w-full" />}>
                <ModerationMetricsDisplay />
              </Suspense>
            </TabsContent>
            <TabsContent value="users">
              <Card>
                <CardHeader>
                  <CardTitle>User Management</CardTitle>
                  <CardDescription>Coming soon: Manage platform users.</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>User management interface will be implemented here.</p>
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="settings">
              <Card>
                <CardHeader>
                  <CardTitle>Platform Settings</CardTitle>
                  <CardDescription>Coming soon: Configure platform settings.</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>Platform settings interface will be implemented here.</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
