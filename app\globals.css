@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 100%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 97%;
    --muted-foreground: 0 0% 30%;
    --accent: 0 0% 0%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 88%;
    --input: 0 0% 88%;
    --ring: 0 0% 88%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    -webkit-tap-highlight-color: transparent;
  }
  html {
    scroll-behavior: smooth;
  }
  /* Improve mobile form elements */
  input,
  textarea,
  button,
  select,
  a {
    -webkit-tap-highlight-color: transparent;
  }
}

.hover-scale {
  transition: transform 0.2s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Remove grayscale filter from brand logos */
.brand-logo {
  transition: all 0.3s ease;
}

.brand-logo:hover {
  transform: scale(1.05);
}

/* Apply grayscale only to flawa images, not logos */
.flawa-image {
  filter: grayscale(100%) contrast(125%);
  transition: all 0.3s ease;
}

.flawa-image:hover {
  filter: grayscale(0%) contrast(100%);
}

/* Global floating Send a Flawa button */
.global-flawa-button {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 50;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.2s ease-in-out;
}

.global-flawa-button:hover {
  transform: translateY(-2px);
}

@media (max-width: 640px) {
  .global-flawa-button {
    bottom: 1.5rem;
    right: 1.5rem;
  }
}

/* Responsive text utilities */
@media (max-width: 640px) {
  h1 {
    font-size: clamp(1.5rem, 5vw, 2.25rem);
    line-height: 1.2;
  }

  h2 {
    font-size: clamp(1.25rem, 4vw, 1.75rem);
    line-height: 1.2;
  }

  h3 {
    font-size: clamp(1.125rem, 3.5vw, 1.5rem);
    line-height: 1.3;
  }

  p {
    font-size: clamp(0.875rem, 3vw, 1rem);
  }
}

/* Improve touch targets on mobile */
@media (max-width: 640px) {
  button,
  .button,
  a.button,
  [role="button"],
  input[type="submit"],
  input[type="reset"],
  input[type="button"] {
    min-height: 44px;
    min-width: 44px;
  }
}
