import { NextResponse } from 'next/server';
import { fetchAllBrandSuggestions } from '@/services/brandSuggestionService';
import { withAdminAuth } from '@/lib/authUtils';
// import { supabase } from '@/lib/supabaseClient'; // No longer needed here

async function handler(request: Request) { // Renamed from GET to handler
  try {
    // Admin check is now handled by withAdminAuth HOF
    const suggestions = await fetchAllBrandSuggestions();
    return NextResponse.json(suggestions);

  } catch (error) {
    console.error('Error in GET /api/admin/brand-suggestions:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to fetch brand suggestions', details: errorMessage }, { status: 500 });
  }
}

export const GET = withAdminAuth(handler);