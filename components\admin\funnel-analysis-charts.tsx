'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, ResponsiveContainer, Toolt<PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2 } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

interface FunnelStep {
  step: string;
  count: number;
  rate?: number; // Conversion rate from previous step
}

interface FunnelData {
  flawaSubmission: FunnelStep[];
  brandCreation: FunnelStep[];
  brandSuggestion: FunnelStep[];
}

interface ApiResponse {
  flawaSubmission?: FunnelStep[];
  brandCreation?: FunnelStep[];
  brandSuggestion?: FunnelStep[];
  error?: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82Ca9D'];

const FunnelChart = ({ data, title }: { data: FunnelStep[]; title: string }) => {
  if (!data || data.length === 0) {
    return <p>No data available for {title}.</p>;
  }

  // Calculate overall conversion from first to last step
  const overallConversion = data.length > 1 ? (data[data.length - 1].count / data[0].count) * 100 : 100;

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>
          Overall conversion: {overallConversion.toFixed(1)}%
          (from {data[0].count.toLocaleString()} to {data[data.length - 1].count.toLocaleString()})
        </CardDescription>
      </CardHeader>
      <CardContent className="h-[400px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} layout="vertical" margin={{ top: 5, right: 30, left: 100, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" stroke="#888888" fontSize={12} />
            <YAxis
              dataKey="step"
              type="category"
              stroke="#888888"
              fontSize={12}
              width={150}
              tickLine={false}
              axisLine={false}
            />
            <Tooltip
              contentStyle={{ backgroundColor: "hsl(var(--background))", border: "1px solid hsl(var(--border))" }}
              labelStyle={{ color: "hsl(var(--foreground))" }}
              formatter={(value: number, name: string, props: any) => {
                const { payload } = props;
                let rateText = "";
                if (payload.rate !== undefined && payload.rate !== 1.0) {
                  rateText = ` (${(payload.rate * 100).toFixed(1)}% from prev)`;
                }
                return [`${value.toLocaleString()}${rateText}`, "Count"];
              }}
            />
            <Legend wrapperStyle={{ paddingTop: '10px' }} />
            <Bar dataKey="count" name="Users in Step" barSize={30}>
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};


export function FunnelAnalysisCharts() {
  const [data, setData] = useState<FunnelData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/admin/funnel-analysis');
        const result: ApiResponse = await response.json();

        if (!response.ok) {
          throw new Error(result.error || `API request failed with status ${response.status}`);
        }
        
        if (result.flawaSubmission && result.brandCreation && result.brandSuggestion) {
            setData({
                flawaSubmission: result.flawaSubmission,
                brandCreation: result.brandCreation,
                brandSuggestion: result.brandSuggestion,
            });
        } else {
            throw new Error("Incomplete funnel data received from API.");
        }

      } catch (e: any) {
        console.error("Failed to fetch funnel analysis data:", e);
        setError(e.message || 'An unknown error occurred.');
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Funnel Analysis</CardTitle>
          <CardDescription>User progression through key flows.</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px] flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <p className="ml-2">Loading funnel data...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Funnel Analysis</CardTitle>
          <CardDescription>User progression through key flows.</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Funnel Analysis</CardTitle>
          <CardDescription>User progression through key flows.</CardDescription>
        </CardHeader>
        <CardContent>
          <p>No funnel data available to display.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Tabs defaultValue="flawaSubmission" className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="flawaSubmission">Flawa Submission</TabsTrigger>
        <TabsTrigger value="brandCreation">Brand Creation</TabsTrigger>
        <TabsTrigger value="brandSuggestion">Brand Suggestion</TabsTrigger>
      </TabsList>
      <TabsContent value="flawaSubmission">
        <FunnelChart data={data.flawaSubmission} title="Flawa Submission Funnel" />
      </TabsContent>
      <TabsContent value="brandCreation">
        <FunnelChart data={data.brandCreation} title="Brand Creation Funnel" />
      </TabsContent>
      <TabsContent value="brandSuggestion">
        <FunnelChart data={data.brandSuggestion} title="Brand Suggestion Funnel" />
      </TabsContent>
    </Tabs>
  );
}