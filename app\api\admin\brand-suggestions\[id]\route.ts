import { NextResponse } from 'next/server';
import { updateBrandSuggestionStatus } from '@/services/brandSuggestionService';
import { withAdminAuth } from '@/lib/authUtils';
// import { supabase } from '@/lib/supabaseClient'; // No longer needed here

interface RouteParams { // Renamed from PatchParams to RouteParams for clarity
  params: {
    id: string;
  };
}

async function patchHandler(request: Request, { params }: RouteParams) { // Renamed PATCH to patchHandler
  try {
    // Admin check is now handled by withAdminAuth HOF
    const { id: suggestionId } = params;
    if (!suggestionId) {
      return NextResponse.json({ error: 'Suggestion ID is required' }, { status: 400 });
    }

    const body = await request.json();
    const { status } = body;

    if (!status || (status !== 'approved' && status !== 'rejected')) {
      return NextResponse.json({ error: 'Invalid status provided. Must be "approved" or "rejected".' }, { status: 400 });
    }

    const updatedSuggestion = await updateBrandSuggestionStatus(suggestionId, status);

    if (!updatedSuggestion) {
      return NextResponse.json({ error: 'Suggestion not found or failed to update' }, { status: 404 });
    }

    return NextResponse.json(updatedSuggestion);

  } catch (error) {
    console.error(`Error in PATCH /api/admin/brand-suggestions/${params.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to update brand suggestion', details: errorMessage }, { status: 500 });
  }
}

export const PATCH = withAdminAuth(patchHandler);