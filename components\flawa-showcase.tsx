"use client"

import Link from "next/link"
import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { FlawaCard } from "@/components/ui/flawa-card"
import { <PERSON>lawa } from "@/types"
import { fetchRecentApprovedFlawas } from "@/services/flawaService"
// formatCount and getFaviconUrl are not directly used in this component anymore
// They are used within FlawaCard, which imports them from lib/utils
import { Skeleton } from "@/components/ui/skeleton" // For loading state

export function FlawaShowcase() {
  const [recentFlawas, setRecentFlawas] = useState<Flawa[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadFlawas = async () => {
      try {
        setIsLoading(true)
        const flawas = await fetchRecentApprovedFlawas(6) // Fetch 6 recent approved flawas
        setRecentFlawas(flawas)
        setError(null)
      } catch (err) {
        console.error("Failed to fetch recent flawas:", err)
        setError(err instanceof Error ? err.message : "An unknown error occurred")
        setRecentFlawas([]) // Clear flawas on error
      } finally {
        setIsLoading(false)
      }
    }

    loadFlawas()
  }, [])

  return (
    <section className="w-full py-12 md:py-24 bg-muted">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Recent Flawas</h2>
            <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
              See the latest love and praise shared by the community.
            </p>
          </div>
        </div>

        {isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
            {Array.from({ length: 6 }).map((_, index) => (
              <CardSkeleton key={index} />
            ))}
          </div>
        )}

        {error && (
          <div className="mt-8 text-center text-red-600">
            <p>Could not load recent flawas: {error}</p>
          </div>
        )}

        {!isLoading && !error && recentFlawas.length === 0 && (
          <div className="mt-8 text-center text-text-secondary">
            <p>No recent flawas to display yet. Be the first to share!</p>
          </div>
        )}

        {!isLoading && !error && recentFlawas.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
            {recentFlawas.map((flawa) => (
              <FlawaCard key={flawa.id} flawa={flawa} />
            ))}
          </div>
        )}

        <div className="flex justify-center mt-10">
          <Link href="/all-flawas">
            <Button className="bg-black text-white hover:bg-gray-800">
              View More Flawas
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}

// Simple skeleton for FlawaCard loading state
function CardSkeleton() {
  return (
    <div className="flex flex-col space-y-3 p-4 border rounded-xl bg-background">
      <Skeleton className="h-[125px] w-full rounded-xl" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </div>
      <div className="flex justify-between items-center pt-2">
        <Skeleton className="h-8 w-20" />
        <Skeleton className="h-8 w-8 rounded-full" />
      </div>
    </div>
  )
}
