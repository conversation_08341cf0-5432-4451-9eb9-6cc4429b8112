"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "sonner"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  ArrowLeft,
  Building,
  ChevronDown,
  Download,
  Eye,
  Flower,
  MoreHorizontal,
  Search,
  Settings,
  Star,
  Trash,
  CheckCircle,
  XCircle,
  Users,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { Brand, <PERSON>law<PERSON> } from "@/types"
import { FlawaStatus } from "@/services/flawaService"
import { use } from "react"

interface BrandDashboardPageProps {
  params: Promise<{ brandDomain: string }>
}

export default function BrandDashboardPage({ params: paramsPromise }: BrandDashboardPageProps) {
  const params = use(paramsPromise)
  const brandDomain = params.brandDomain.toLowerCase()

  const [activeTab, setActiveTab] = useState("overview")
  const [brand, setBrand] = useState<Brand | null>(null)
  const [isLoadingBrand, setIsLoadingBrand] = useState(true)
  const [brandError, setBrandError] = useState<string | null>(null)

  const [brandFlawas, setBrandFlawas] = useState<Flawa[]>([])
  const [isLoadingFlawas, setIsLoadingFlawas] = useState(true)
  const [flawasError, setFlawasError] = useState<string | null>(null)

  // Fetch brand data
  useEffect(() => {
    const fetchBrand = async () => {
      setIsLoadingBrand(true)
      setBrandError(null)
      try {
        const response = await fetch(`/api/brands/by-domain/${brandDomain}`)
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || `Failed to fetch brand: ${response.statusText}`)
        }
        const data: Brand = await response.json()
        setBrand(data)
      } catch (error) {
        console.error("Error fetching brand:", error)
        setBrandError(error instanceof Error ? error.message : "An unknown error occurred")
      } finally {
        setIsLoadingBrand(false)
      }
    }

    fetchBrand()
  }, [brandDomain])

  // Fetch brand flawas
  useEffect(() => {
    if (!brand?.id) return

    const fetchBrandFlawas = async () => {
      setIsLoadingFlawas(true)
      setFlawasError(null)
      try {
        const response = await fetch(`/api/brands/${brand.id}/flawas`)
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || `Failed to fetch flawas: ${response.statusText}`)
        }
        const data: Flawa[] = await response.json()
        setBrandFlawas(data)
      } catch (error) {
        console.error("Error fetching brand flawas:", error)
        setFlawasError(error instanceof Error ? error.message : "An unknown error occurred")
      } finally {
        setIsLoadingFlawas(false)
      }
    }

    fetchBrandFlawas()
  }, [brand?.id])

  const handleFlawaStatusUpdate = async (flawaId: string, newStatus: FlawaStatus) => {
    const originalFlawas = [...brandFlawas]
    setBrandFlawas(prev => prev.map(f => f.id === flawaId ? { ...f, status: newStatus } : f))

    try {
      const response = await fetch(`/api/brands/${brand?.id}/flawas/${flawaId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus }),
      })
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to update Flawa status: ${response.statusText}`)
      }
      toast.success(`Flawa ${newStatus} successfully`)
    } catch (error) {
      console.error(`Error updating Flawa status for ${flawaId}:`, error)
      setBrandFlawas(originalFlawas)
      toast.error(`Failed to update Flawa status: ${error instanceof Error ? error.message : "Unknown error"}`)
    }
  }

  const handleFlawaDelete = async (flawaId: string) => {
    if (!confirm("Are you sure you want to delete this Flawa? This action cannot be undone.")) {
      return
    }
    const originalFlawas = [...brandFlawas]
    setBrandFlawas(prev => prev.filter(f => f.id !== flawaId))

    try {
      const response = await fetch(`/api/brands/${brand?.id}/flawas/${flawaId}`, {
        method: 'DELETE',
      })
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to delete Flawa: ${response.statusText}`)
      }
      toast.success("Flawa deleted successfully")
    } catch (error) {
      console.error(`Error deleting Flawa ${flawaId}:`, error)
      setBrandFlawas(originalFlawas)
      toast.error(`Failed to delete Flawa: ${error instanceof Error ? error.message : "Unknown error"}`)
    }
  }

  // Get favicon URL as fallback for brand logo
  const getFaviconUrl = (domain: string, size = 128): string => {
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
  }

  if (isLoadingBrand) {
    return (
      <div className="flex min-h-screen flex-col">
        <main className="flex-1 bg-muted/30">
          <div className="container px-4 md:px-6 py-8">
            <Skeleton className="h-8 w-64 mb-4" />
            <Skeleton className="h-32 w-full" />
          </div>
        </main>
      </div>
    )
  }

  if (brandError || !brand) {
    return (
      <div className="flex min-h-screen flex-col">
        <main className="flex-1 bg-muted/30">
          <div className="container px-4 md:px-6 py-8">
            <div className="text-center">
              <h1 className="text-2xl font-bold mb-4">Brand Not Found</h1>
              <p className="text-muted-foreground mb-4">
                {brandError || "The requested brand could not be found."}
              </p>
              <Button asChild>
                <Link href="/">Return Home</Link>
              </Button>
            </div>
          </div>
        </main>
      </div>
    )
  }

  const pendingFlawas = brandFlawas.filter(f => f.status === 'pending')
  const approvedFlawas = brandFlawas.filter(f => f.status === 'approved')
  const rejectedFlawas = brandFlawas.filter(f => f.status === 'rejected')

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1 bg-muted/30">
        <div className="container px-4 md:px-6 py-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/${brandDomain}`}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Brand Page
                </Link>
              </Button>
              <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-16 h-16 shadow-card">
                <Image
                  src={brand.logo || getFaviconUrl(brand.domain)}
                  alt={brand.name}
                  width={64}
                  height={64}
                  className="brand-logo"
                />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h1 className="text-2xl font-bold">{brand.name} Dashboard</h1>
                  <Badge>{brand.tier}</Badge>
                  {brand.isClaimed && <Badge variant="secondary">Claimed</Badge>}
                </div>
                <p className="text-text-secondary">Manage your brand's flawas and settings.</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Settings className="h-4 w-4" />
                <span>Settings</span>
              </Button>
            </div>
          </div>

          <Tabs defaultValue="overview" className="space-y-4" onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 md:grid-cols-4 w-full">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="flawas">
                Flawas <Badge variant="outline" className="ml-1.5">{brandFlawas.length}</Badge>
              </TabsTrigger>
              <TabsTrigger value="pending">
                Pending <Badge variant="outline" className="ml-1.5">{pendingFlawas.length}</Badge>
              </TabsTrigger>
              <TabsTrigger value="analytics" className="hidden md:block">
                Analytics
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Flawas</CardTitle>
                    <Flower className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{brandFlawas.length}</div>
                    <p className="text-xs text-muted-foreground">All time</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Approved</CardTitle>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{approvedFlawas.length}</div>
                    <p className="text-xs text-muted-foreground">Live on site</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Pending</CardTitle>
                    <XCircle className="h-4 w-4 text-yellow-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{pendingFlawas.length}</div>
                    <p className="text-xs text-muted-foreground">Awaiting review</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Rejected</CardTitle>
                    <XCircle className="h-4 w-4 text-red-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{rejectedFlawas.length}</div>
                    <p className="text-xs text-muted-foreground">Not approved</p>
                  </CardContent>
                </Card>
              </div>

              <div className="grid gap-4 md:grid-cols-1">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Flawas</CardTitle>
                    <CardDescription>Latest flawas submitted for your brand.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {isLoadingFlawas ? (
                      <div className="space-y-2">
                        {Array.from({ length: 3 }).map((_, index) => (
                          <Skeleton key={index} className="h-16 w-full" />
                        ))}
                      </div>
                    ) : flawasError ? (
                      <div className="text-center text-red-500">
                        Error loading flawas: {flawasError}
                      </div>
                    ) : brandFlawas.length === 0 ? (
                      <div className="text-center text-muted-foreground py-8">
                        No flawas found for your brand yet.
                      </div>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Content</TableHead>
                            <TableHead>Author</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {brandFlawas.slice(0, 5).map((flawa) => (
                            <TableRow key={flawa.id}>
                              <TableCell className="max-w-[200px] truncate" title={flawa.content}>
                                {flawa.content}
                              </TableCell>
                              <TableCell>{flawa.author || "Anonymous"}</TableCell>
                              <TableCell>
                                <Badge variant={
                                  flawa.status === "pending" ? "destructive" :
                                  flawa.status === "approved" ? "default" : "secondary"
                                }>
                                  {flawa.status ? flawa.status.charAt(0).toUpperCase() + flawa.status.slice(1) : "Unknown"}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem>
                                      <Eye className="mr-2 h-4 w-4" />
                                      <span>View Details</span>
                                    </DropdownMenuItem>
                                    {flawa.status === "pending" && (
                                      <>
                                        <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'approved')}>
                                          <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                          <span>Approve</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'rejected')}>
                                          <XCircle className="mr-2 h-4 w-4 text-orange-500" />
                                          <span>Reject</span>
                                        </DropdownMenuItem>
                                      </>
                                    )}
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem className="text-red-600 hover:!text-red-600" onClick={() => handleFlawaDelete(flawa.id)}>
                                      <Trash className="mr-2 h-4 w-4" />
                                      <span>Delete</span>
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="flawas" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>All Flawas</CardTitle>
                  <CardDescription>Manage all flawas for your brand.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <div className="relative">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input placeholder="Search flawas..." className="pl-8 w-[250px]" />
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" className="flex items-center gap-1">
                            <span>Filter</span>
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>All</DropdownMenuItem>
                          <DropdownMenuItem>Approved</DropdownMenuItem>
                          <DropdownMenuItem>Pending</DropdownMenuItem>
                          <DropdownMenuItem>Rejected</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <Button className="flex items-center gap-1">
                      <Download className="h-4 w-4" />
                      <span>Export</span>
                    </Button>
                  </div>
                  {isLoadingFlawas ? (
                    <div className="space-y-2">
                      {Array.from({ length: 5 }).map((_, index) => (
                        <Skeleton key={index} className="h-16 w-full" />
                      ))}
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Content</TableHead>
                          <TableHead>Author</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {brandFlawas.map((flawa) => (
                          <TableRow key={flawa.id}>
                            <TableCell className="max-w-[200px] truncate" title={flawa.content}>
                              {flawa.content}
                            </TableCell>
                            <TableCell>{flawa.author || "Anonymous"}</TableCell>
                            <TableCell>{flawa.timestamp ? new Date(flawa.timestamp).toLocaleDateString() : "N/A"}</TableCell>
                            <TableCell>
                              <Badge variant={
                                flawa.status === "pending" ? "destructive" :
                                flawa.status === "approved" ? "default" : "secondary"
                              }>
                                {flawa.status ? flawa.status.charAt(0).toUpperCase() + flawa.status.slice(1) : "Unknown"}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    <span>View Details</span>
                                  </DropdownMenuItem>
                                  {flawa.status === "pending" && (
                                    <>
                                      <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'approved')}>
                                        <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                        <span>Approve</span>
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'rejected')}>
                                        <XCircle className="mr-2 h-4 w-4 text-orange-500" />
                                        <span>Reject</span>
                                      </DropdownMenuItem>
                                    </>
                                  )}
                                  {flawa.status === "approved" && (
                                    <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'pending')}>
                                      <XCircle className="mr-2 h-4 w-4 text-yellow-500" />
                                      <span>Unapprove</span>
                                    </DropdownMenuItem>
                                  )}
                                  {flawa.status === "rejected" && (
                                    <DropdownMenuItem onClick={() => handleFlawaStatusUpdate(flawa.id, 'pending')}>
                                      <CheckCircle className="mr-2 h-4 w-4 text-yellow-500" />
                                      <span>Move to Pending</span>
                                    </DropdownMenuItem>
                                  )}
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-red-600 hover:!text-red-600" onClick={() => handleFlawaDelete(flawa.id)}>
                                    <Trash className="mr-2 h-4 w-4" />
                                    <span>Delete</span>
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="pending" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Pending Flawas</CardTitle>
                  <CardDescription>Review and approve flawas waiting for your approval.</CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoadingFlawas ? (
                    <div className="space-y-2">
                      {Array.from({ length: 3 }).map((_, index) => (
                        <Skeleton key={index} className="h-16 w-full" />
                      ))}
                    </div>
                  ) : pendingFlawas.length === 0 ? (
                    <div className="text-center text-muted-foreground py-8">
                      No pending flawas to review.
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Content</TableHead>
                          <TableHead>Author</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {pendingFlawas.map((flawa) => (
                          <TableRow key={flawa.id}>
                            <TableCell className="max-w-[300px]" title={flawa.content}>
                              {flawa.content}
                            </TableCell>
                            <TableCell>{flawa.author || "Anonymous"}</TableCell>
                            <TableCell>{flawa.timestamp ? new Date(flawa.timestamp).toLocaleDateString() : "N/A"}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex gap-2 justify-end">
                                <Button
                                  size="sm"
                                  onClick={() => handleFlawaStatusUpdate(flawa.id, 'approved')}
                                  className="bg-green-600 hover:bg-green-700"
                                >
                                  <CheckCircle className="mr-1 h-4 w-4" />
                                  Approve
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleFlawaStatusUpdate(flawa.id, 'rejected')}
                                  className="border-orange-500 text-orange-600 hover:bg-orange-50"
                                >
                                  <XCircle className="mr-1 h-4 w-4" />
                                  Reject
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Analytics</CardTitle>
                  <CardDescription>View detailed analytics for your brand.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center text-muted-foreground py-8">
                    Analytics dashboard coming soon. This will include detailed metrics about your brand's performance.
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
