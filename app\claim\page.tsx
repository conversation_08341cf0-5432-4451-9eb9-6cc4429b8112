"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { GlobalFlawaButton } from "@/components/global-flawa-button"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Search, Building, ArrowRight, Flower, Check, CheckCircle } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { toast } from "sonner"
import { ClaimAttempt, ClaimStatus, VerificationMethod } from "@/types" // Import claim types

// Mock data for brands
const mockBrands = [
  {
    id: "apple",
    name: "Apple",
    domain: "apple.com",
    category: "Technology",
    flawaCount: 1243,
    isClaimed: false,
  },
  {
    id: "nike",
    name: "<PERSON>",
    domain: "nike.com",
    category: "Fashion",
    flawaCount: 987,
    isClaimed: true,
  },
  {
    id: "spotify",
    name: "Spotify",
    domain: "spotify.com",
    category: "Entertainment",
    flawaCount: 756,
    isClaimed: false,
  },
  {
    id: "tesla",
    name: "Tesla",
    domain: "tesla.com",
    category: "Automotive",
    flawaCount: 642,
    isClaimed: true,
  },
  {
    id: "airbnb",
    name: "Airbnb",
    domain: "airbnb.com",
    category: "Travel",
    flawaCount: 531,
    isClaimed: false,
  },
]

// Format flawa count (e.g., 1000 -> 1K)
const formatCount = (count: number): string => {
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1).replace(/\.0$/, "")}K`
  }
  return count.toString()
}

// Get favicon URL as fallback for brand logo
const getFaviconUrl = (domain: string, size = 128): string => {
  return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
}

// Mock in-memory store for claim attempts (in a real app, this would be a database)
const mockClaimAttempts: ClaimAttempt[] = [];

function generateId() {
  return Math.random().toString(36).substr(2, 9);
}

export default function ClaimPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [searchResults, setSearchResults] = useState<typeof mockBrands>([])
  const [isSearching, setIsSearching] = useState(false)
  const [selectedBrand, setSelectedBrand] = useState<(typeof mockBrands)[0] | null>(null)
  const [activeTab, setActiveTab] = useState("find")
  const [formSubmitted, setFormSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [verificationStep, setVerificationStep] = useState<'email' | 'fallback'>('email')
  const [formData, setFormData] = useState({
    name: '', // For "Find Brand" -> Email/Fallback: Contact Name
    email: '', // For "Find Brand" -> Email/Fallback: Work Email (<EMAIL>)
    role: '', // For "Find Brand" -> Email/Fallback: Role
    personalEmail: '', // For "Find Brand" -> Fallback: Personal Email
    socialProfile: '', // For "Find Brand" -> Fallback: Social Profile

    // For "Request New Brand" tab
    request_brandName: '',
    request_brandDomain: '',
    request_brandCategory: '',
    request_brandDescription: '',
    request_contactName: '',
    request_contactEmail: '',
    request_contactRole: '',
    request_howHeard: '',
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [emailDomainError, setEmailDomainError] = useState<string | null>(null)

  // Check for brandName in URL on component mount
  useEffect(() => {
    const params = new URLSearchParams(window.location.search)
    const brandName = params.get('brand')
    
    if (brandName) {
      setSearchTerm(brandName)
      // Trigger search
      const results = mockBrands.filter(
        (b) => 
          !b.isClaimed && (
            b.name.toLowerCase().includes(brandName.toLowerCase()) ||
            b.domain.toLowerCase().includes(brandName.toLowerCase())
          )
      )
      setSearchResults(results)
      setIsSearching(true)
      
      // If there's exactly one match, select it automatically
      if (results.length === 1) {
        setSelectedBrand(results[0])
        setActiveTab('claim')
      }
    }
    
    setIsLoading(false)
  }, [])

  const handleBrandSelect = (brand: (typeof mockBrands)[0]) => {
    setSelectedBrand(brand)
    setSearchTerm(brand.name) // Keep the search term with the selected brand name
    setIsSearching(false)
    setActiveTab('claim')
  }

  const handleSearch = () => {
    if (searchTerm.length > 0) {
      setIsSearching(true)
      // Filter brands based on search term and only show unclaimed ones
      const results = mockBrands.filter(
        (brand) =>
          !brand.isClaimed && (  // Only include unclaimed brands
            brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            brand.domain.toLowerCase().includes(searchTerm.toLowerCase())
          )
      )
      setSearchResults(results)
    } else {
      setIsSearching(false)
      setSearchResults([])
    }
  }

  // Update URL when selected brand changes
  useEffect(() => {
    if (selectedBrand) {
      const url = new URL(window.location.href)
      url.searchParams.set('brandId', selectedBrand.id)
      window.history.replaceState({}, '', url.toString())
    }
  }, [selectedBrand])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission logic here
    setFormSubmitted(true)
  }

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Check if email domain matches brand domain
    const emailDomain = formData.email.split('@')[1]
    if (emailDomain !== selectedBrand?.domain) {
      setEmailDomainError(`Email domain must match ${selectedBrand?.domain}`)
      setIsSubmitting(false)
      return
    }

    // Simulate API call to send verification email
    setTimeout(() => {
      setIsSubmitting(false)
      setFormSubmitted(true)

      const newClaim: ClaimAttempt = {
        attemptId: generateId(),
        brandId: selectedBrand?.id,
        brandName: selectedBrand?.name || "Unknown Brand",
        contactName: formData.name,
        contactEmail: formData.email,
        contactRole: formData.role,
        verificationMethod: "email",
        status: "pending_email_verification",
        attemptTimestamp: new Date().toISOString(),
        claimedDomain: selectedBrand?.domain,
      };
      mockClaimAttempts.push(newClaim);
      console.log("New Email Claim Attempt:", newClaim);
      toast.success("Verification email sent! Please check your inbox.")
    }, 1000)
  }

  const handleFallbackSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate API call for manual verification
    setTimeout(() => {
      setIsSubmitting(false)
      setFormSubmitted(true)
      const newClaim: ClaimAttempt = {
        attemptId: generateId(),
        brandId: selectedBrand?.id,
        brandName: selectedBrand?.name || "Unknown Brand",
        contactName: formData.name,
        contactEmail: formData.personalEmail, // Using personalEmail for fallback
        contactRole: formData.role,
        verificationMethod: "fallback_manual",
        status: "pending_manual_review",
        attemptTimestamp: new Date().toISOString(),
        claimedDomain: selectedBrand?.domain,
        requestedSocialProfile: formData.socialProfile,
      };
      mockClaimAttempts.push(newClaim);
      console.log("New Fallback Claim Attempt:", newClaim);
      toast.success("Your verification request has been submitted. Our team will review it shortly.")
    }, 1000)
  }

const handleNewBrandRequestSubmit = (e?: React.FormEvent) => {
  if (e) e.preventDefault();
  setIsSubmitting(true);

  if (!formData.request_brandName || !formData.request_brandDomain || !formData.request_contactEmail || !formData.request_contactName) {
    toast.error("Please fill in all required fields for the new brand request (Brand Name, Website, Your Name, Your Email).");
    setIsSubmitting(false);
    return;
  }

  // Simulate API call
  setTimeout(() => {
    setIsSubmitting(false);
    setFormSubmitted(true); // This shows the generic success page for the whole claim card
    const newClaim: ClaimAttempt = {
      attemptId: generateId(),
      brandName: formData.request_brandName,
      contactName: formData.request_contactName,
      contactEmail: formData.request_contactEmail,
      contactRole: formData.request_contactRole,
      verificationMethod: "request_new_brand",
      status: "pending_new_brand_review",
      attemptTimestamp: new Date().toISOString(),
      requestedBrandDescription: formData.request_brandDescription,
      claimedDomain: formData.request_brandDomain, // Store the requested domain
      // userId could be added if user is logged in
    };
    mockClaimAttempts.push(newClaim);
    console.log("New Brand Request Claim:", newClaim, mockClaimAttempts);
    toast.success("New brand request submitted! Our team will review it.");
  }, 1000);
};

  const benefits = [
    {
      title: "Customize Your Profile",
      description: "Add your logo, update your description, and showcase your brand identity.",
      icon: <Building className="h-10 w-10" />,
    },
    {
      title: "Highlight Your Favorite Flawas",
      description: "Feature the best praise from your customers at the top of your page.",
      icon: <Flower className="h-10 w-10" />,
    },
    {
      title: "Access Analytics",
      description: "Get insights on views, engagement, and trends for your Flawagram page.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M3 3v18h18" />
          <path d="M18 9l-6-6-7 7" />
          <path d="M14 9h4v4" />
        </svg>
      ),
    },
  ]

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <main className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        <section className="w-full py-12 md:py-24 bg-muted">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center space-y-4 text-center">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl">
                  Claim Your Flawagram Page
                </h1>
                <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
                  Take control of your brand's Flawagram and showcase the love you receive from your customers.
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="w-full py-12 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="grid gap-10 lg:grid-cols-2 items-start">
              <div>
                <h2 className="text-3xl font-bold mb-6">Why Claim Your Page?</h2>
                <div className="space-y-8">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="flex gap-4">
                      <div className="shrink-0 text-black">{benefit.icon}</div>
                      <div>
                        <h3 className="text-xl font-bold">{benefit.title}</h3>
                        <p className="text-text-secondary">{benefit.description}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-10 p-6 bg-muted rounded-xl">
                  <h3 className="text-xl font-bold mb-4">How It Works</h3>
                  <ol className="space-y-4">
                    <li className="flex gap-3">
                      <div className="flex h-7 w-7 items-center justify-center rounded-full bg-black text-white shrink-0">
                        1
                      </div>
                      <div>
                        <p className="font-medium">Search for your brand</p>
                        <p className="text-sm text-text-secondary">
                          Find your existing Flawagram page or request a new one.
                        </p>
                      </div>
                    </li>
                    <li className="flex gap-3">
                      <div className="flex h-7 w-7 items-center justify-center rounded-full bg-black text-white shrink-0">
                        2
                      </div>
                      <div>
                        <p className="font-medium">Verify ownership</p>
                        <p className="text-sm text-text-secondary">
                          Confirm you're authorized to manage this brand through domain verification.
                        </p>
                      </div>
                    </li>
                    <li className="flex gap-3">
                      <div className="flex h-7 w-7 items-center justify-center rounded-full bg-black text-white shrink-0">
                        3
                      </div>
                      <div>
                        <p className="font-medium">Choose your plan</p>
                        <p className="text-sm text-text-secondary">Select a subscription plan that fits your needs.</p>
                      </div>
                    </li>
                    <li className="flex gap-3">
                      <div className="flex h-7 w-7 items-center justify-center rounded-full bg-black text-white shrink-0">
                        4
                      </div>
                      <div>
                        <p className="font-medium">Customize your page</p>
                        <p className="text-sm text-text-secondary">
                          Add your logo, update information, and highlight your favorite Flawas.
                        </p>
                      </div>
                    </li>
                  </ol>
                </div>
              </div>

              <div>
                {formSubmitted ? (
                  <Card className="border-border shadow-card rounded-xl">
                    <CardHeader>
                      <div className="flex items-center justify-center mb-4">
                        <CheckCircle className="h-16 w-16 text-green-500" />
                      </div>
                      <CardTitle className="text-center">Request Submitted!</CardTitle>
                      <CardDescription className="text-center">
                        Thank you for your interest in claiming your Flawagram page.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="text-center">
                      <p className="mb-6">
                        Our team will review your request and contact you within 1-2 business days to guide you through
                        the next steps.
                      </p>
                      <div className="bg-muted p-4 rounded-lg mb-6">
                        <h3 className="font-medium mb-2">What happens next?</h3>
                        <ul className="space-y-2 text-left">
                          <li className="flex items-start gap-2">
                            <Check className="h-5 w-5 text-green-500 mt-0.5 shrink-0" />
                            <span>We'll verify your brand ownership</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <Check className="h-5 w-5 text-green-500 mt-0.5 shrink-0" />
                            <span>You'll receive access to your brand dashboard</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <Check className="h-5 w-5 text-green-500 mt-0.5 shrink-0" />
                            <span>We'll help you set up your subscription plan</span>
                          </li>
                        </ul>
                      </div>
                    </CardContent>
                    <CardFooter className="flex flex-col gap-4">
                      <Button asChild className="w-full">
                        <Link href="/pricing">View Pricing Plans</Link>
                      </Button>
                      <Button asChild variant="outline" className="w-full">
                        <Link href="/">Return to Homepage</Link>
                      </Button>
                    </CardFooter>
                  </Card>
                ) : (
                  <Card className="border-border shadow-card rounded-xl">
                    <CardHeader>
                      <CardTitle>Claim Your Brand</CardTitle>
                      <CardDescription>Search for your brand or request a new Flawagram page.</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Tabs defaultValue="find" className="w-full" onValueChange={setActiveTab}>
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="find">Find Your Brand</TabsTrigger>
                          <TabsTrigger value="request">Request New Brand</TabsTrigger>
                        </TabsList>
                        <TabsContent value="find" className="mt-6">
                          <div className="space-y-4">
                            <div className="space-y-2">
                              <Label htmlFor="brand-search">Search for your brand</Label>
                              <div className="relative">
                                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input
                                  id="brand-search"
                                  placeholder="Enter brand name or domain..."
                                  className="pl-10 pr-24"
                                  value={searchTerm}
                                  onChange={(e) => setSearchTerm(e.target.value)}
                                />
                                <Button 
                                  className="absolute right-1 top-1/2 -translate-y-1/2 h-9" 
                                  onClick={handleSearch}
                                >
                                  Search
                                </Button>
                              </div>
                            </div>

                            {isSearching && (
                              <div className="space-y-4 mt-4">
                                <h3 className="font-medium">Search Results</h3>
                                {searchResults.length > 0 ? (
                                  <div className="space-y-3">
                                    {searchResults.map((brand) => (
                                      <div
                                        key={brand.id}
                                        className="flex items-center p-3 hover:bg-muted rounded-lg transition-colors cursor-pointer border"
                                        onClick={() => handleBrandSelect(brand)}
                                      >
                                        <div className="rounded-xl overflow-hidden bg-muted flex items-center justify-center w-12 h-12 mr-4">
                                          <Image
                                            src={getFaviconUrl(brand.domain) || "/placeholder.svg?height=48&width=48"}
                                            alt={brand.name}
                                            width={48}
                                            height={48}
                                            className="brand-logo"
                                          />
                                        </div>
                                        <div className="flex-1">
                                          <h3 className="font-medium">{brand.name}</h3>
                                          <div className="flex items-center mt-1 space-x-2">
                                            <Badge variant="outline" className="text-xs">
                                              {brand.category}
                                            </Badge>
                                            <span className="text-xs text-text-secondary flex items-center">
                                              <Flower className="h-3 w-3 mr-1" />
                                              {formatCount(brand.flawaCount)}
                                            </span>
                                          </div>
                                        </div>
                                        <Button variant="ghost" size="sm">
                                          Select
                                        </Button>
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <div className="text-center p-6 border rounded-lg">
                                    <p className="text-text-secondary mb-4">No brands found matching "{searchTerm}"</p>
                                    <Button variant="outline" onClick={() => setActiveTab("request")}>
                                      Request a New Brand
                                    </Button>
                                  </div>
                                )}
                              </div>
                            )}

                            {selectedBrand && (
                              <div className="mt-6 space-y-4">
                                <h3 className="font-medium">Selected Brand</h3>
                                <div className="p-4 border rounded-lg">
                                  <div className="flex items-center">
                                    <div className="rounded-xl overflow-hidden bg-muted flex items-center justify-center w-16 h-16 mr-4">
                                      <Image
                                        src={getFaviconUrl(selectedBrand.domain) || "/placeholder.svg?height=64&width=64"}
                                        alt={selectedBrand.name}
                                        width={64}
                                        height={64}
                                        className="brand-logo"
                                      />
                                    </div>
                                    <div>
                                      <h3 className="font-bold text-lg">{selectedBrand.name}</h3>
                                      <div className="flex items-center mt-1 space-x-2">
                                        <Badge variant="outline">{selectedBrand.category}</Badge>
                                        <span className="text-sm text-text-secondary flex items-center">
                                          <Flower className="h-4 w-4 mr-1" />
                                          {formatCount(selectedBrand.flawaCount)} Flawas
                                        </span>
                                      </div>
                                    </div>
                                  </div>

                                  {verificationStep === 'email' ? (
                                    <form onSubmit={handleEmailSubmit} className="mt-6 space-y-4">
                                      <div className="space-y-2">
                                        <Label htmlFor="contact-name">Your Name</Label>
                                        <Input
                                          id="contact-name"
                                          placeholder="Full name"
                                          value={formData.name}
                                          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                          required
                                        />
                                      </div>
                                      <div className="space-y-2">
                                        <Label htmlFor="contact-email">Work Email</Label>
                                        <Input
                                          id="contact-email"
                                          type="email"
                                          placeholder={`you@${selectedBrand.domain}`}
                                          value={formData.email}
                                          onChange={(e) => {
                                            setFormData({ ...formData, email: e.target.value })
                                            setEmailDomainError(null)
                                          }}
                                          className={emailDomainError ? 'border-red-500' : ''}
                                          required
                                        />
                                        {emailDomainError ? (
                                          <p className="text-xs text-red-500">{emailDomainError}</p>
                                        ) : (
                                          <p className="text-xs text-text-secondary">
                                            Must match your brand's domain ({selectedBrand.domain}) for verification.
                                          </p>
                                        )}
                                      </div>
                                      <div className="space-y-2">
                                        <Label htmlFor="contact-role">Your Role</Label>
                                        <Input
                                          id="contact-role"
                                          placeholder="e.g. Marketing Manager"
                                          value={formData.role}
                                          onChange={(e) => setFormData({ ...formData, role: e.target.value })}
                                          required
                                        />
                                      </div>
                                      <Button type="submit" className="w-full mt-6" disabled={isSubmitting}>
                                        {isSubmitting ? 'Sending Verification...' : 'Send Verification Email'}
                                      </Button>
                                      <div className="text-center mt-4">
                                        <Button
                                          type="button"
                                          variant="link"
                                          className="text-sm text-gray-500"
                                          onClick={() => setVerificationStep('fallback')}
                                        >
                                          Don't have a company email? Click here for alternative verification
                                        </Button>
                                      </div>
                                    </form>
                                  ) : (
                                    <form onSubmit={handleFallbackSubmit} className="mt-6 space-y-4">
                                      <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                                        <p className="text-sm text-gray-600">
                                          Please provide additional information to help us verify your connection to {selectedBrand.name}.
                                          Our team will review your request and may follow up with additional questions.
                                        </p>
                                      </div>
                                      <div className="space-y-2">
                                        <Label htmlFor="personal-email">Personal Email</Label>
                                        <Input
                                          id="personal-email"
                                          type="email"
                                          placeholder="<EMAIL>"
                                          value={formData.personalEmail}
                                          onChange={(e) => setFormData({ ...formData, personalEmail: e.target.value })}
                                          required
                                        />
                                      </div>
                                      <div className="space-y-2">
                                        <Label htmlFor="social-profile">Professional Social Profile</Label>
                                        <Input
                                          id="social-profile"
                                          placeholder="LinkedIn or Twitter profile URL"
                                          value={formData.socialProfile}
                                          onChange={(e) => setFormData({ ...formData, socialProfile: e.target.value })}
                                          required
                                        />
                                      </div>
                                      <div className="space-y-2">
                                        <Label htmlFor="verification-method">Verification Documents</Label>
                                        <select
                                          id="verification-method"
                                          className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-black focus:border-black"
                                          value={formData.verificationMethod}
                                          onChange={(e) => setFormData({ ...formData, verificationMethod: e.target.value })}
                                          required
                                        >
                                          <option value="">Select verification method</option>
                                          <option value="company-id">Company ID or Business Card</option>
                                          <option value="letter">Letter of Authorization</option>
                                          <option value="contract">Employment Contract</option>
                                          <option value="other">Other Documentation</option>
                                        </select>
                                      </div>
                                      <Button type="submit" className="w-full mt-6" disabled={isSubmitting}>
                                        {isSubmitting ? 'Submitting Request...' : 'Submit Verification Request'}
                                      </Button>
                                      <div className="text-center mt-4">
                                        <Button
                                          type="button"
                                          variant="link"
                                          className="text-sm text-gray-500"
                                          onClick={() => setVerificationStep('email')}
                                        >
                                          Back to email verification
                                        </Button>
                                      </div>
                                    </form>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </TabsContent>

                        <TabsContent value="request" className="mt-6">
                          <form className="space-y-4" onSubmit={handleNewBrandRequestSubmit}>
                            <div className="space-y-2">
                              <Label htmlFor="request-brand-name">Brand Name</Label>
                              <Input id="request-brand-name" placeholder="Your brand name" required
                                value={formData.request_brandName}
                                onChange={(e) => setFormData({...formData, request_brandName: e.target.value})}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="request-brand-domain">Brand Website</Label>
                              <Input id="request-brand-domain" placeholder="https://yourbrand.com" required
                                value={formData.request_brandDomain}
                                onChange={(e) => setFormData({...formData, request_brandDomain: e.target.value})}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="request-brand-category">Category</Label>
                              <Input id="request-brand-category" placeholder="e.g. Technology, Fashion, Food" required
                                value={formData.request_brandCategory}
                                onChange={(e) => setFormData({...formData, request_brandCategory: e.target.value})}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="request-brand-description">Brief Description</Label>
                              <Textarea
                                id="request-brand-description"
                                placeholder="Tell us about your brand"
                                className="min-h-[100px]"
                                required
                                value={formData.request_brandDescription}
                                onChange={(e) => setFormData({...formData, request_brandDescription: e.target.value})}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="request-contact-name">Your Name</Label>
                              <Input id="request-contact-name" placeholder="Full name" required
                                value={formData.request_contactName}
                                onChange={(e) => setFormData({...formData, request_contactName: e.target.value})}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="request-contact-email">Your Email</Label>
                              <Input id="request-contact-email" type="email" placeholder="<EMAIL>" required
                                value={formData.request_contactEmail}
                                onChange={(e) => setFormData({...formData, request_contactEmail: e.target.value})}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="request-contact-role">Your Role</Label>
                              <Input id="request-contact-role" placeholder="e.g. Marketing Manager"
                                value={formData.request_contactRole}
                                onChange={(e) => setFormData({...formData, request_contactRole: e.target.value})}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="request-how-heard">How did you hear about Flawagram?</Label>
                              <select
                                id="request-how-heard"
                                className="w-full h-10 px-3 py-2 border border-input bg-background rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-ring"
                                value={formData.request_howHeard}
                                onChange={(e) => setFormData({...formData, request_howHeard: e.target.value})}
                              >
                                <option value="">Select an option</option>
                                <option value="search">Search engine</option>
                                <option value="social">Social media</option>
                                <option value="friend">Friend or colleague</option>
                                <option value="press">Press or news article</option>
                                <option value="other">Other</option>
                              </select>
                            </div>
                             {/* The button is now in CardFooter, form submission handled by onSubmit */}
                          </form>
                        </TabsContent>
                      </Tabs>
                    </CardContent>
                    <CardFooter>
                      <Button
                        className="w-full"
                        onClick={(e) => {
                          if (activeTab === 'request') {
                            handleNewBrandRequestSubmit(e);
                          } else {
                            // For "find" tab, or other cases, use the generic handleSubmit
                            // This generic one just sets formSubmitted to true.
                            // Actual claim for existing brand is via handleEmailSubmit or handleFallbackSubmit
                            handleSubmit(e);
                          }
                        }}
                        disabled={isSubmitting}
                        // If the form is the one for "request", this button can be type="submit" for that form.
                        // However, since the button is outside the form structure of "request" tab, direct onClick is better.
                        // To make it submit the form programmatically: document.getElementById('new-brand-request-form-id')?.requestSubmit();
                        // For now, direct call to handler is fine.
                      >
                        {isSubmitting ? "Submitting..." :
                          (activeTab === "find" && selectedBrand
                            ? "Claim This Brand" // This button's action for "Claim This Brand" is still generic
                            : activeTab === "request"
                              ? "Submit Request"
                              : "Continue")}
                      </Button>
                    </CardFooter>
                  </Card>
                )}

                <div className="mt-8 text-center">
                  <p className="text-text-secondary mb-4">Want to see our pricing plans before claiming?</p>
                  <Button asChild variant="outline" className="border-black hover:bg-hover hover:text-white">
                    <Link href="/pricing">
                      View Pricing Plans <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>

            <div className="mt-20">
              <div className="text-center mb-10">
                <h2 className="text-3xl font-bold">Brands Who've Claimed Their Flawagram</h2>
                <p className="text-text-secondary mt-2">
                  Join these brands who are showcasing the love they receive from their customers.
                </p>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8">
                {mockBrands.map((brand) => (
                  <div key={brand.id} className="flex flex-col items-center text-center">
                    <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-20 h-20 mb-4 shadow-sm">
                      <Image
                        src={getFaviconUrl(brand.domain) || "/placeholder.svg?height=80&width=80"}
                        alt={brand.name}
                        width={80}
                        height={80}
                        className="brand-logo"
                      />
                    </div>
                    <h3 className="font-bold">{brand.name}</h3>
                    <div className="flex items-center mt-1">
                      <Flower className="h-4 w-4 mr-1" />
                      <span className="text-sm text-text-secondary">{formatCount(brand.flawaCount)} Flawas</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      </main>
      <GlobalFlawaButton />
    </div>
  )
}
