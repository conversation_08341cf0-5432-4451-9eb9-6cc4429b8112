import { BrandReferralSources } from '@/components/brand/brand-referral-sources';
import { BrandComparativeAnalytics } from '@/components/brand/brand-comparative-analytics';
import { Suspense } from 'react';
import { Skeleton } from "@/components/ui/skeleton";
// Removed Card imports from here as they are used by the components themselves or not directly needed at page level for this change.
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

// TODO: Add proper authentication and authorization checks.
// This page should only be accessible to users who manage this specific brand.
// You might fetch brand details here to display the brand name.

interface BrandDashboardPageProps {
  params: {
    id: string; // This is the brandId from the URL
  };
}

export default async function BrandDashboardPage({ params }: BrandDashboardPageProps) {
  const brandId = params.id;

  // Placeholder for fetching brand details (e.g., name)
  // const brand = await getBrandDetails(brandId); // You'd need to implement this
  const brandName = `Brand ${brandId}`; // Fallback name

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <Button variant="outline" size="sm" asChild className="mb-2">
            <Link href={`/brands/${brandId}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Brand Page
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-xl text-muted-foreground">For {brandName}</p>
        </div>
        {/* Add other controls or info here if needed */}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6"> {/* Adjusted for two columns on medium screens */}
        <section>
          <Suspense fallback={<Skeleton className="h-[500px] w-full" />}> {/* Increased height for referral chart + table */}
            <BrandReferralSources brandId={brandId} />
          </Suspense>
        </section>
        
        <section>
          <Suspense fallback={<Skeleton className="h-[300px] w-full" />}>
            <BrandComparativeAnalytics brandId={brandId} />
          </Suspense>
        </section>
      </div>
    </div>
  );
}