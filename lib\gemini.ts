/**
 * Utility functions for interacting with the Google Gemini API
 */

// Define the response structure from Gemini API
interface GeminiResponse {
  text: string;
  error?: string;
}

/**
 * Enhances a message with emojis using Google Gemini API
 *
 * @param message - The original message to enhance
 * @param maxLength - Maximum character length (default: 280)
 * @returns The enhanced message with emojis
 */
export async function enhanceMessageWithEmojis(
  message: string,
  maxLength: number = 500
): Promise<GeminiResponse> {
  try {
    // Check if message is empty
    if (!message.trim()) {
      return { text: message, error: "Message is empty" };
    }

    // Check if message is too long before sending to API
    if (message.length > maxLength) {
      return {
        text: message.substring(0, maxLength),
        error: `Message exceeds maximum length of ${maxLength} characters`
      };
    }

    // Add timeout to prevent long-running requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      // Call our API route instead of directly calling Gemini API
      const response = await fetch('/api/enhance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          maxLength,
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId); // Clear timeout if request completes

      // Parse the response
      const data = await response.json();

      // Check for errors in the API response
      if (!response.ok) {
        console.error("API error:", data);
        return {
          text: message,
          error: data.error || "Failed to enhance message"
        };
      }

      // Validate the response
      if (!data.text) {
        return { text: message, error: "Invalid response from API" };
      }

      return { text: data.text };
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof DOMException && error.name === 'AbortError') {
        return { text: message, error: "Request timed out" };
      }

      throw error; // Re-throw for outer catch block
    }
  } catch (error) {
    console.error("Error enhancing message:", error);
    return {
      text: message,
      error: "Failed to enhance message"
    };
  }
}
