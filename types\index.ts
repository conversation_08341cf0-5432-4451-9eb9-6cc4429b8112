/**
 * Social media links for a Flawa
 */
export interface SocialLinks {
  twitter?: string;
  instagram?: string;
  linkedin?: string;
}

/**
 * Represents a piece of brand appreciation content
 */
export interface Flawa {
  id: string;
  type: "tweet" | "image" | "video" | "text";
  content: string;
  author: string;
  authorImage?: string;
  brand: string;
  brandDomain: string;
  likes: number;
  views?: number; // Added views property
  source?: string;
  image?: string;
  timestamp?: string;
  socialLinks?: SocialLinks;
  isLiked?: boolean;
  user_id?: string | null; // ID of the user who submitted the flawa
  status?: string; // Added status for approved, pending, etc.
  tags?: string[]; // Added tags property
}

/**
 * Props for the FlawaCard component
 */
export interface FlawaCardProps {
  /** The Flawa content to display */
  flawa: Flawa;
  /** Visual style variant of the card */
  variant?: "default" | "minimal" | "brand-page";
  /** Callback when share button is clicked */
  onShare?: (flawa: Flawa) => Promise<void>;
  /** Whether to show brand information */
  showBrandInfo?: boolean;
}

// SharePlatform type is no longer needed as Web Share API handles platform choice

export interface Brand {
  id: string;
  name: string;
  logo?: string | null;
  domain: string;
  description?: string;
  website?: string;
  category: string;
  flawaCount: number;
  isClaimed?: boolean;
  tier?: "Free" | "Pro";
}

/**
 * Defines the possible roles a user can have.
 */
export type UserRole = "sitewideAdmin" | "pageAdmin" | "user";

/**
 * Represents a user in the system.
 */
export interface User {
  id: string;
  email: string;
  name?: string;
  avatarUrl?: string;
  role: UserRole;
  signupDate?: string; // Added signupDate
  /**
   * Array of brand IDs that the user manages.
   * Applicable only if role is 'pageAdmin'.
   */
  managedBrandIds?: string[];
}

export type ClaimStatus =
  | "pending_email_verification"
  | "pending_manual_review"
  | "pending_new_brand_review"
  | "approved"
  | "rejected"
  | "failed_email_match";

export type VerificationMethod = "email" | "fallback_manual" | "request_new_brand";

export interface ClaimAttempt {
  attemptId: string;
  brandId?: string; // Optional if it's a new brand request
  brandName: string; // Name of the brand being claimed or requested
  userId?: string; // ID of the user making the claim, if logged in
  contactName: string;
  contactEmail: string; // Email used for the claim
  contactRole?: string; // Role at the company
  verificationMethod: VerificationMethod;
  status: ClaimStatus;
  attemptTimestamp: string;
  resolutionTimestamp?: string;
  adminNotes?: string;
  claimedDomain?: string; // The domain they are trying to claim (e.g., from selectedBrand.domain)
  requestedSocialProfile?: string; // For fallback or new brand
  requestedBrandDescription?: string; // For new brand request
}