import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Flower } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { SubmissionForm } from "@/components/submission-form"

export function Hero() {
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-muted">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center space-y-6 text-center">
          <div className="space-y-3">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl">
              Where Brands Celebrate All The Love They Receive
            </h1>
            <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
              Flawagram collects and organizes love for brands in one beautiful, curated space.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-3 w-full justify-center">
            <Button asChild size="lg" className="w-full sm:w-auto">
              <Link href="/brands">Explore Brands</Link>
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="lg"
                  className="w-full sm:w-auto bg-black text-white hover:bg-gray-800 border border-black flex items-center gap-2"
                >
                  <Flower className="h-4 w-4" />
                  <span>Send a Flawa</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Send a Flawa</DialogTitle>
                  <DialogDescription>Share your love for a brand. No account required.</DialogDescription>
                </DialogHeader>
                <SubmissionForm inDialog={true} />
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </section>
  )
}
