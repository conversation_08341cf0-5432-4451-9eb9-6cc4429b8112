import { supabase } from '@/lib/supabaseClient';
import { Brand } from '@/types'; // Assuming Brand type is defined in types/index.ts

/**
 * Fetches all brands from the database.
 * flawaCount is defaulted to 0 as per current strategy.
 * @returns A promise that resolves to an array of Brand objects.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchAllBrands = async (): Promise<Brand[]> => {
  const { data, error } = await supabase
    .from('brands')
    .select(`
      id,
      name,
      logo_url,
      domain,
      description,
      website_url,
      category,
      is_claimed,
      tier
    `)
    .order('name', { ascending: true });

  if (error) {
    console.error('Error fetching all brands:', error);
    throw new Error(error.message);
  }

  if (!data) {
    return [];
  }

  // Map Supabase data to Brand type
  return data.map((item: any) => ({
    id: item.id,
    name: item.name,
    logo: item.logo_url, // Map logo_url to logo
    domain: item.domain,
    description: item.description,
    website: item.website_url, // Map website_url to website
    category: item.category,
    isClaimed: item.is_claimed,
    tier: item.tier,
    flawaCount: 0, // Defaulting to 0 for now
  } as Brand)); // Added 'as Brand' for type assertion and ensured closing brace
};

// More functions will be added below:
// - fetchBrandById (will attempt to calculate flawaCount)
// - fetchClaimedBrands / fetchUnclaimedBrands
// - handle brand creation/suggestion

/**
 * Fetches a single brand by its ID from the database.
 * Also calculates the flawaCount for this brand.
 * @param brandId The UUID of the brand.
 * @returns A promise that resolves to a Brand object or null if not found.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchBrandById = async (brandId: string): Promise<Brand | null> => {
  if (!brandId) {
    console.warn('fetchBrandById called without brandId');
    return null;
  }

  const { data: brandData, error: brandError } = await supabase
    .from('brands')
    .select(`
      id,
      name,
      logo_url,
      domain,
      description,
      website_url,
      category,
      is_claimed,
      tier
    `)
    .eq('id', brandId)
    .single(); // Expecting a single row

  if (brandError) {
    if (brandError.code === 'PGRST116') { // PostgREST error for "Searched for one row, but found 0"
      console.log(`Brand with ID ${brandId} not found.`);
      return null;
    }
    console.error(`Error fetching brand ${brandId}:`, brandError);
    throw new Error(brandError.message);
  }

  if (!brandData) {
    return null;
  }

  // Fetch flawa count for this brand
  const { count: flawaCount, error: countError } = await supabase
    .from('flawas')
    .select('*', { count: 'exact', head: true })
    .eq('brand_id', brandId)
    .eq('status', 'approved'); // Only count approved flawas

  if (countError) {
    console.error(`Error fetching flawa count for brand ${brandId}:`, countError);
    // Don't throw, just default count to 0 or handle as needed
  }

  return {
    id: brandData.id,
    name: brandData.name,
    logo: brandData.logo_url,
    domain: brandData.domain,
    description: brandData.description,
    website: brandData.website_url,
    category: brandData.category,
    isClaimed: brandData.is_claimed,
    tier: brandData.tier,
    flawaCount: flawaCount || 0,
  } as Brand;
};

/**
 * Fetches all claimed brands from the database.
 * @returns A promise that resolves to an array of claimed Brand objects.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchClaimedBrands = async (): Promise<Brand[]> => {
  const { data, error } = await supabase
    .from('brands')
    .select(`
      id,
      name,
      logo_url,
      domain,
      description,
      website_url,
      category,
      is_claimed,
      tier
    `)
    .eq('is_claimed', true)
    .order('name', { ascending: true });

  if (error) {
    console.error('Error fetching claimed brands:', error);
    throw new Error(error.message);
  }

  if (!data) {
    return [];
  }

  return data.map((item: any) => ({
    id: item.id,
    name: item.name,
    logo: item.logo_url,
    domain: item.domain,
    description: item.description,
    website: item.website_url,
    category: item.category,
    isClaimed: item.is_claimed,
    tier: item.tier,
    flawaCount: 0, // Defaulting to 0 for now, consider calculating if needed for this view
  } as Brand));
};

/**
 * Fetches all unclaimed brands from the database.
 * @returns A promise that resolves to an array of unclaimed Brand objects.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchUnclaimedBrands = async (): Promise<Brand[]> => {
  const { data, error } = await supabase
    .from('brands')
    .select(`
      id,
      name,
      logo_url,
      domain,
      description,
      website_url,
      category,
      is_claimed,
      tier
    `)
    .eq('is_claimed', false)
    .order('name', { ascending: true });

  if (error) {
    console.error('Error fetching unclaimed brands:', error);
    throw new Error(error.message);
  }

  if (!data) {
    return [];
  }

  return data.map((item: any) => ({
    id: item.id,
    name: item.name,
    logo: item.logo_url,
    domain: item.domain,
    description: item.description,
    website: item.website_url,
    category: item.category,
    isClaimed: item.is_claimed,
    tier: item.tier,
    flawaCount: 0, // Defaulting to 0 for now
  } as Brand));
};

/**
 * Interface for the data required to create a new Brand.
 */
export interface CreateBrandData {
  name: string;
  logo_url?: string;
  domain?: string;
  description?: string;
  website_url?: string;
  category?: string;
  // is_claimed will default to false, claimed_by_user_id to null, tier to 'Free'
}

/**
 * Creates a new brand in the database.
 * @param brandData The data for the new brand.
 * @returns A promise that resolves to the newly created Brand object.
 * @throws Throws an error if the creation operation fails.
 */
export const createBrand = async (brandData: CreateBrandData): Promise<Brand> => {
  const { data, error } = await supabase
    .from('brands')
    .insert([
      {
        name: brandData.name,
        logo_url: brandData.logo_url,
        domain: brandData.domain,
        description: brandData.description,
        website_url: brandData.website_url,
        category: brandData.category,
        // is_claimed, claimed_by_user_id, tier will use DB defaults
      },
    ])
    .select(`
      id,
      name,
      logo_url,
      domain,
      description,
      website_url,
      category,
      is_claimed,
      tier
    `)
    .single();

  if (error) {
    console.error('Error creating brand:', error);
    // Consider more specific error handling, e.g., for unique constraint violations
    if (error.code === '23505') { // Unique violation
        throw new Error(`A brand with this name or domain already exists. Original error: ${error.message}`);
    }
    throw new Error(error.message);
  }

  if (!data) {
    throw new Error('Failed to create brand: No data returned.');
  }

  const item = data as any;
  return {
    id: item.id,
    name: item.name,
    logo: item.logo_url,
    domain: item.domain,
    description: item.description,
    website: item.website_url,
    category: item.category,
    isClaimed: item.is_claimed,
    tier: item.tier,
    flawaCount: 0, // New brands start with 0 flawas
  } as Brand;
};

/**
 * Fetches featured brands, ordered by their (approved) flawa count.
 * Note: This implementation fetches all brands and then their flawa counts individually.
 * For performance with many brands, consider a Supabase Edge Function or a database view
 * that pre-calculates or efficiently queries flawa counts.
 * @param limit The maximum number of featured brands to fetch.
 * @returns A promise that resolves to an array of Brand objects.
 */
export const fetchFeaturedBrandsFromService = async (limit: number = 4): Promise<Brand[]> => {
  // 1. Fetch all brands (or a subset if you have criteria for "featurable" brands)
  const { data: brandsData, error: brandsError } = await supabase
    .from('brands')
    .select(`
      id,
      name,
      logo_url,
      domain,
      description,
      website_url,
      category,
      is_claimed,
      tier
    `)
    // Add any initial filtering if needed, e.g., .eq('is_active', true)
    .limit(100); // Limit initial fetch if there are too many brands to process client-side

  if (brandsError) {
    console.error('Error fetching brands for featured list:', brandsError);
    throw new Error(brandsError.message);
  }

  if (!brandsData) {
    return [];
  }

  // 2. For each brand, fetch its approved flawa count
  const brandsWithCounts = await Promise.all(
    brandsData.map(async (brand) => {
      const { count, error: countError } = await supabase
        .from('flawas')
        .select('*', { count: 'exact', head: true })
        .eq('brand_id', brand.id)
        .eq('status', 'approved');

      if (countError) {
        console.error(`Error fetching flawa count for brand ${brand.id}:`, countError);
        // Assign 0 or handle as per your error strategy
      }
      return {
        ...brand,
        flawaCount: count || 0,
      };
    })
  );

  // 3. Sort brands by flawaCount descending, then by name
  brandsWithCounts.sort((a, b) => {
    if (b.flawaCount !== a.flawaCount) {
      return b.flawaCount - a.flawaCount;
    }
    return a.name.localeCompare(b.name);
  });

  // 4. Return the top 'limit' brands
  const featuredBrands = brandsWithCounts.slice(0, limit);

  // 5. Map to the Brand type
  return featuredBrands.map(item => ({
    id: item.id,
    name: item.name,
    logo: item.logo_url,
    domain: item.domain,
    description: item.description,
    website: item.website_url,
    category: item.category,
    isClaimed: item.is_claimed,
    tier: item.tier,
    flawaCount: item.flawaCount, // This is the calculated count
  } as Brand));
};

/**
 * Searches for brands by name (case-insensitive).
 * Calculates flawaCount for each found brand.
 * @param searchTerm The term to search for in brand names.
 * @param limit Optional limit for the number of results.
 * @returns A promise that resolves to an array of Brand objects.
 */
export const searchBrandsByName = async (searchTerm: string, limit: number = 10): Promise<Brand[]> => {
  if (!searchTerm || searchTerm.trim() === "") {
    return [];
  }

  const { data: brandsData, error: brandsError } = await supabase
    .from('brands')
    .select(`
      id,
      name,
      logo_url,
      domain,
      description,
      website_url,
      category,
      is_claimed,
      tier
    `)
    .ilike('name', `%${searchTerm}%`) // Case-insensitive search
    .limit(limit);

  if (brandsError) {
    console.error(`Error searching brands for "${searchTerm}":`, brandsError);
    throw new Error(brandsError.message);
  }

  if (!brandsData) {
    return [];
  }

  // For each found brand, fetch its approved flawa count
  const brandsWithCounts = await Promise.all(
    brandsData.map(async (brand) => {
      const { count, error: countError } = await supabase
        .from('flawas')
        .select('*', { count: 'exact', head: true })
        .eq('brand_id', brand.id)
        .eq('status', 'approved');

      if (countError) {
        console.error(`Error fetching flawa count for brand ${brand.id} during search:`, countError);
      }
      return {
        ...brand,
        flawaCount: count || 0,
      };
    })
  );
  
  // Map to the Brand type
  return brandsWithCounts.map(item => ({
    id: item.id,
    name: item.name,
    logo: item.logo_url,
    domain: item.domain,
    description: item.description,
    website: item.website_url,
    category: item.category,
    isClaimed: item.is_claimed,
    tier: item.tier,
    flawaCount: item.flawaCount,
  } as Brand));
};

/**
 * Fetches the total count of all brands.
 * @returns A promise that resolves to the total number of brands.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchTotalBrandsCount = async (): Promise<number> => {
  const { count, error } = await supabase
    .from('brands')
    .select('*', { count: 'exact', head: true });

  if (error) {
    console.error('Error fetching total brands count:', error);
    throw new Error(error.message);
  }
  return count ?? 0;
};

/**
 * Fetches the count of brands with 'pending' status.
 * Assumes a 'status' column in the 'brands' table.
 * @returns A promise that resolves to the number of pending brands.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchPendingBrandsCount = async (): Promise<number> => {
  const { count, error } = await supabase
    .from('brands')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'pending'); // Assumes 'status' column exists

  if (error) {
    console.error('Error fetching pending brands count:', error);
    throw new Error(error.message);
  }
  return count ?? 0;
};

/**
 * Fetches the N most recent brands, including their approved flawa count.
 * Assumes a 'created_at' timestamp column in the 'brands' table for ordering.
 * @param limit The maximum number of recent brands to fetch.
 * @returns A promise that resolves to an array of Brand objects.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchRecentBrandsWithFlawaCount = async (limit: number = 5): Promise<Brand[]> => {
  const { data: brandsData, error: brandsError } = await supabase
    .from('brands')
    .select(`
      id,
      name,
      logo_url,
      domain,
      description,
      website_url,
      category,
      is_claimed,
      tier,
      status,
      created_at
    `)
    .order('created_at', { ascending: false })
    .limit(limit);

  if (brandsError) {
    console.error('Error fetching recent brands:', brandsError);
    throw new Error(brandsError.message);
  }

  if (!brandsData) {
    return [];
  }

  const brandsWithCounts = await Promise.all(
    brandsData.map(async (brand) => {
      const { count, error: countError } = await supabase
        .from('flawas')
        .select('*', { count: 'exact', head: true })
        .eq('brand_id', brand.id)
        .eq('status', 'approved');

      if (countError) {
        console.error(`Error fetching flawa count for brand ${brand.id}:`, countError);
      }
      return {
        id: brand.id,
        name: brand.name,
        logo: brand.logo_url,
        domain: brand.domain,
        description: brand.description,
        website: brand.website_url,
        category: brand.category,
        isClaimed: brand.is_claimed,
        tier: brand.tier,
        status: brand.status, // This is the status from the DB
        flawaCount: count || 0,
        // created_at: brand.created_at // Optionally include if needed in Brand type
      } as Brand & { status?: string }; // Extend Brand type or adjust as needed
        })
      );

  return brandsWithCounts;
};

/**
 * Defines the possible statuses for a Brand for admin actions.
 */
export type BrandAdminStatus = 'pending' | 'approved' | 'rejected'; // Add other statuses if they exist

/**
 * Updates the status of a specific brand.
 * @param brandId The ID of the brand to update.
 * @param status The new status for the brand.
 * @returns A promise that resolves to the updated Brand object or null if not found.
 * @throws Throws an error if the update operation fails.
 */
export const updateBrandStatus = async (brandId: string, status: BrandAdminStatus): Promise<Brand | null> => {
  const { data, error } = await supabase
    .from('brands')
    .update({ status: status, updated_at: new Date().toISOString() }) // Assuming an 'updated_at' column
    .eq('id', brandId)
    .select(`
      id, name, logo_url, domain, description, website_url, category, is_claimed, tier, status, created_at
    `) // Ensure all relevant fields, including status, are selected
    .single();

  if (error) {
    console.error(`Error updating brand ${brandId} to status ${status}:`, error);
    throw new Error(error.message);
  }
  if (!data) return null;

  // Map to Brand type, including the new status
  const item = data as any;
  return {
    id: item.id,
    name: item.name,
    logo: item.logo_url,
    domain: item.domain,
    description: item.description,
    website: item.website_url,
    category: item.category,
    isClaimed: item.is_claimed,
    tier: item.tier,
    status: item.status, // Include status
    flawaCount: 0, // Recalculate or fetch if necessary, or accept it might be stale after status update
    // created_at: item.created_at, // if needed
  } as Brand & { status?: string };
};

/**
 * Toggles the featured status of a specific brand.
 * @param brandId The ID of the brand to update.
 * @param isFeatured The new featured status (true or false).
 * @returns A promise that resolves to the updated Brand object or null if not found.
 * @throws Throws an error if the update operation fails.
 */
export const toggleBrandFeaturedStatus = async (brandId: string, isFeatured: boolean): Promise<Brand | null> => {
  const { data, error } = await supabase
    .from('brands')
    .update({ is_featured: isFeatured, updated_at: new Date().toISOString() }) // Assuming 'is_featured' and 'updated_at' columns
    .eq('id', brandId)
    .select(`
      id, name, logo_url, domain, description, website_url, category, is_claimed, tier, status, is_featured, created_at
    `) // Ensure all relevant fields are selected
    .single();

  if (error) {
    console.error(`Error toggling featured status for brand ${brandId}:`, error);
    throw new Error(error.message);
  }
  if (!data) return null;
  
  const item = data as any;
  return {
    id: item.id,
    name: item.name,
    logo: item.logo_url,
    domain: item.domain,
    description: item.description,
    website: item.website_url,
    category: item.category,
    isClaimed: item.is_claimed,
    tier: item.tier,
    status: item.status,
    isFeatured: item.is_featured, // Include featured status
    flawaCount: 0, // Recalculate or fetch if necessary
  } as Brand & { status?: string; isFeatured?: boolean };
};

/**
 * Deletes a specific brand.
 * @param brandId The ID of the brand to delete.
 * @returns A promise that resolves when the operation is complete.
 * @throws Throws an error if the delete operation fails.
 */
export const deleteBrand = async (brandId: string): Promise<void> => {
  // Consider implications: what happens to flawas associated with this brand?
  // Depending on DB schema (cascade delete, set null, restrict), this might need more handling.
  const { error } = await supabase
    .from('brands')
    .delete()
    .eq('id', brandId);

  if (error) {
    console.error(`Error deleting brand ${brandId}:`, error);
    throw new Error(error.message);
  }
};

/**
 * Fetches the count of brands created in a given month.
 * Assumes a 'created_at' timestamp column in the 'brands' table.
 * @param date The date to determine the month and year.
 * @returns A promise that resolves to the number of brands created in that month.
 * @throws Throws an error if the fetch operation fails.
 */
export const fetchBrandsCreatedInMonthCount = async (date: Date): Promise<number> => {
  const year = date.getFullYear();
  const month = date.getMonth(); // 0-indexed (0 for January, 11 for December)

  const firstDayOfMonth = new Date(year, month, 1).toISOString();
  const firstDayOfNextMonth = new Date(year, month + 1, 1).toISOString();

  const { count, error } = await supabase
    .from('brands')
    .select('*', { count: 'exact', head: true })
    .gte('created_at', firstDayOfMonth) // Assumes 'created_at' column exists
    .lt('created_at', firstDayOfNextMonth);

  if (error) {
    console.error('Error fetching brands created in month count:', error);
    throw new Error(error.message);
  }
  return count ?? 0;
};