"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { EnhancedTextarea } from "@/components/ui/enhanced-textarea"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import NextLink from "next/link" // Renamed to avoid conflict with Lucide's Link
import { Link as LucideLink, Upload, Search, X, Flower } from "lucide-react"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { useAuth } from "@/components/auth-provider" // Added useAuth
import { formatCount, getFaviconUrl } from "@/lib/utils"
import { create<PERSON><PERSON><PERSON>, CreateFlawaData } from "@/services/flawaService"
import { searchBrandsByName, fetchBrandById } from "@/services/brandService" // Import brand services
import { Brand } from "@/types" // Ensure Brand type is imported
import { useDebounce } from "@/hooks/use-debounce"
import { Loader2 } from "lucide-react" // For loading indicator

// Removed mockBrands array

interface SubmissionFormProps {
  inDialog?: boolean
  selectedBrandId?: string
}

export function SubmissionForm({ inDialog = false, selectedBrandId }: SubmissionFormProps) {
  const [submissionType, setSubmissionType] = useState("text")
  const [searchTerm, setSearchTerm] = useState("")
  const debouncedSearchTerm = useDebounce(searchTerm, 300)
  const [searchResults, setSearchResults] = useState<Brand[]>([])
  const [isSearching, setIsSearching] = useState(false) // Used for loading state of search
  const [searchError, setSearchError] = useState<string | null>(null)
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null)
  const [isLoadingInitialBrand, setIsLoadingInitialBrand] = useState(false)
  const [socialLinks, setSocialLinks] = useState({
    twitter: "",
    instagram: "",
    linkedin: "",
  })
  const [tags, setTags] = useState<string[]>([])
  const [tagInput, setTagInput] = useState("")
  const MAX_TAGS = 5
  const MAX_TAG_LENGTH = 20
  const [messageContent, setMessageContent] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({})
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const { user } = useAuth() // Get user from auth context

  // Set the selected brand if selectedBrandId is provided
  useEffect(() => {
    const loadInitialBrand = async () => {
      if (selectedBrandId) {
        setIsLoadingInitialBrand(true);
        setSearchError(null);
        try {
          const brand = await fetchBrandById(selectedBrandId);
          if (brand) {
            setSelectedBrand(brand);
            setSearchTerm(brand.name); // Pre-populate search term
          } else {
            setSearchError(`Brand with ID ${selectedBrandId} not found.`);
            toast.error(`Could not load brand: ${selectedBrandId}`);
          }
        } catch (error) {
          console.error("Error fetching initial brand:", error);
          setSearchError(error instanceof Error ? error.message : "Failed to load initial brand.");
          toast.error("Failed to load initial brand.");
        } finally {
          setIsLoadingInitialBrand(false);
        }
      }
    };
    loadInitialBrand();
  }, [selectedBrandId]);

  useEffect(() => {
    const performBrandSearch = async () => {
      if (debouncedSearchTerm.length < 2 || selectedBrand?.name === debouncedSearchTerm) {
        // Don't search if term is too short or if it matches the already selected brand's name
        // (to prevent re-searching when a brand is selected and its name populates the search field)
        setSearchResults([]);
        setIsSearching(false); // Ensure loading state is off
        return;
      }

      setIsSearching(true);
      setSearchError(null);
      try {
        const results = await searchBrandsByName(debouncedSearchTerm, 5); // Limit to 5 results for dropdown
        setSearchResults(results);
      } catch (error) {
        console.error("Error searching brands:", error);
        setSearchError(error instanceof Error ? error.message : "Failed to search brands.");
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    performBrandSearch();
  }, [debouncedSearchTerm, selectedBrand?.name]); // Add selectedBrand.name to dependencies

  const handleBrandSelect = (brand: Brand) => {
    setSelectedBrand(brand);
    setSearchTerm(brand.name); // Set search term to selected brand's name
    setSearchResults([]); // Clear search results
    setIsSearching(false); // Stop searching state
  }

  const handleTagInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      addTag()
    }
  }

  const addTag = () => {
    const trimmedInput = tagInput.trim()

    // Don't add empty tags
    if (!trimmedInput) return

    // Don't add duplicate tags (case insensitive)
    if (tags.some(tag => tag.toLowerCase() === trimmedInput.toLowerCase())) {
      setTagInput('')
      return
    }

    // Limit number of tags
    if (tags.length >= MAX_TAGS) {
      toast?.error(`Maximum ${MAX_TAGS} tags allowed`)
      setTagInput('')
      return
    }

    // Limit tag length
    const finalTag = trimmedInput.length > MAX_TAG_LENGTH
      ? trimmedInput.substring(0, MAX_TAG_LENGTH)
      : trimmedInput

    setTags([...tags, finalTag])
    setTagInput('')
  }

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (value.endsWith(',')) {
      setTagInput(value.slice(0, -1))
      addTag()
    } else {
      setTagInput(value)
    }
  }

  const validateForm = (): boolean => {
    const errors: {[key: string]: string} = {}

    // Validate brand selection
    if (!selectedBrand) {
      errors.brand = "Please select a brand"
    }

    // Validate message content
    if (!messageContent.trim()) {
      errors.message = "Please enter your message"
    } else if (messageContent.length > 500) {
      errors.message = "Message is too long (maximum 500 characters)"
    }

    // Set errors and return validation result
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      toast.error("Please fix the errors in the form")
      return
    }
    if (!selectedBrand) { // Should be caught by validateForm, but as an extra check
        toast.error("Brand not selected.");
        return;
    }

    setIsSubmitting(true)

    let serviceSubmitType: CreateFlawaData['type'];
    let sourceUrlValue: string | undefined = undefined;
    let imageUrlValue: string | undefined = undefined;

    // Assuming state variables linkUrl, imageUrl, videoUrl exist and are populated
    // For example: const [linkUrl, setLinkUrl] = useState(""); etc.
    // These would be set by inputs further down in the form for those submission types.
    const linkUrl = (document.getElementById('link') as HTMLInputElement)?.value;
    const imageUrl = (document.getElementById('image-url') as HTMLInputElement)?.value; // Assuming an input with id 'image-url'
    const videoUrl = (document.getElementById('video-url') as HTMLInputElement)?.value; // Assuming an input with id 'video-url'


    switch (submissionType) {
      case "text":
        serviceSubmitType = "text";
        break;
      case "link":
        serviceSubmitType = "text"; // Storing general links as "text" type with a source_url
        sourceUrlValue = linkUrl;
        break;
      case "image":
        serviceSubmitType = "image";
        imageUrlValue = imageUrl;
        break;
      case "video":
        serviceSubmitType = "video";
        sourceUrlValue = videoUrl; // Videos are linked via source_url
        break;
      default:
        toast.error("Invalid submission type selected.");
        setIsSubmitting(false);
        return;
    }

    let authorName = "Anonymous Contributor";
    let authorImgUrl: string | undefined = undefined;

    if (user) {
      authorName = (user.user_metadata?.name as string) || user.email || "User";
      authorImgUrl = user.user_metadata?.avatar_url as string | undefined;
    }
    
    const flawaDataToSubmit: CreateFlawaData = {
      type: serviceSubmitType,
      content: messageContent, // Assuming messageContent is available and validated for all types
      author: authorName,
      author_image_url: authorImgUrl,
      brand_id: selectedBrand.id, // selectedBrand is asserted non-null after validation
      source_url: sourceUrlValue,
      image_url: imageUrlValue,
      social_links: socialLinks,
      user_id: user?.id || null,
      tags: tags,
    };

    try {
      console.log("Attempting to submit Flawa:", flawaDataToSubmit);
      const newFlawa = await createFlawa(flawaDataToSubmit);
      console.log("Flawa submitted successfully:", newFlawa);

      toast.success("Thank you for your submission! Your flawa will be reviewed by our team.")

      // Reset form
      setSelectedBrand(null)
      setSubmissionType("text")
      setMessageContent("")
      setSocialLinks({
        twitter: "",
        instagram: "",
        linkedin: "",
      })
      setTags([])
      setTagInput("")
      setFormErrors({})

      // Close dialog if in dialog mode
      if (inDialog) {
        // Find the closest dialog element and use the ESC key to close it
        const closeEvent = new KeyboardEvent('keydown', {
          key: 'Escape',
          bubbles: true
        });
        document.dispatchEvent(closeEvent);
      }
    } catch (error) {
      console.error("Error submitting flawa:", error)
      toast.error("Failed to submit your flawa. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className={inDialog ? "" : "w-full py-6 md:py-12"}>
      <div className={inDialog ? "" : "container px-4 md:px-6"}>
        {!inDialog && (
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Send a Flawa</h2>
              <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
                Share your love for a brand. {user ? `You are submitting as ${user.email}.` : "No account required."}
              </p>
            </div>
          </div>
        )}
        <Card className={`${inDialog ? "" : "max-w-2xl mx-auto mt-8"} border-border shadow-card rounded-xl`}>
          {!inDialog && (
            <CardHeader>
              <CardTitle>Submit Your Praise</CardTitle>
              <CardDescription>Your submission will be reviewed by our team before being published.</CardDescription>
            </CardHeader>
          )}
          <CardContent className={inDialog ? "pt-4" : ""}>
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-2">
                <Label htmlFor="brand">Brand Name</Label>
                {!selectedBrand ? (
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="brand"
                      placeholder="Search for a brand..."
                      className={`pl-10 ${formErrors.brand ? 'border-red-500' : ''}`}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      aria-describedby={formErrors.brand ? "brand-error" : undefined}
                      disabled={isLoadingInitialBrand}
                    />
                    {isSearching && <Loader2 className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 animate-spin" />}
                    {formErrors.brand && (
                      <p id="brand-error" className="text-xs text-red-500 mt-1">
                        {formErrors.brand}
                      </p>
                    )}
                  </div>
                ) : null}
                
                {isLoadingInitialBrand && (
                    <div className="mt-2 p-3 bg-muted rounded-lg flex items-center justify-center">
                        <Loader2 className="h-5 w-5 animate-spin mr-2" /> Loading brand...
                    </div>
                )}

                {/* Show search results dropdown only if not loading initial brand, and search term is active */}
                {!isLoadingInitialBrand && debouncedSearchTerm.length > 1 && !selectedBrand && (searchResults.length > 0 || isSearching || searchError) && (
                  <Card className="absolute z-10 w-full mt-1 max-h-[200px] overflow-y-auto shadow-lg border">
                    <CardContent className="p-2">
                      {isSearching && searchResults.length === 0 && !searchError && (
                        <div className="p-2 text-center text-text-secondary">
                          <Loader2 className="h-4 w-4 mx-auto animate-spin" /> Searching...
                        </div>
                      )}
                      {searchError && (
                        <div className="p-2 text-center text-red-500">Error: {searchError}</div>
                      )}
                      {!isSearching && !searchError && searchResults.length > 0 && (
                        <div className="space-y-1">
                          {searchResults.map((brand) => (
                            <div
                              key={brand.id}
                              className="flex items-center p-2 hover:bg-muted rounded-md transition-colors cursor-pointer"
                              onClick={() => handleBrandSelect(brand)}
                            >
                              <div className="rounded-lg overflow-hidden bg-muted flex items-center justify-center w-8 h-8 mr-2 shrink-0">
                                <Image
                                  src={brand.logo || getFaviconUrl(brand.domain)}
                                  alt={brand.name}
                                  width={32}
                                  height={32}
                                  className="object-contain"
                                />
                              </div>
                              <div className="flex-1 min-w-0">
                                <h3 className="font-medium text-sm truncate">{brand.name}</h3>
                                <div className="flex items-center mt-0.5 space-x-1 text-xs text-text-secondary">
                                  <Badge variant="outline" className="text-xs py-0 px-1">
                                    {brand.category || "N/A"}
                                  </Badge>
                                  <span>·</span>
                                  <Flower className="h-3 w-3" />
                                  <span>{formatCount(brand.flawaCount)}</span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                      {!isSearching && !searchError && searchResults.length === 0 && debouncedSearchTerm.length > 1 && (
                        <div className="p-4 text-center text-text-secondary">
                          <p>No brands found matching "{debouncedSearchTerm}".</p>
                          <NextLink href="/suggest-brand" passHref>
                            <Button variant="link" className="text-sm mt-1 h-auto p-0">
                              Suggest this brand?
                            </Button>
                          </NextLink>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

                {!isLoadingInitialBrand && selectedBrand && (
                  <div className="mt-2 p-3 bg-muted rounded-lg flex items-center">
                    <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-12 h-12 mr-4">
                      <Image
                        src={selectedBrand.logo || getFaviconUrl(selectedBrand.domain)}
                        alt={selectedBrand.name}
                        width={48}
                        height={48}
                        className="brand-logo"
                      />
                    </div>
                    <div>
                      <h3 className="font-medium">{selectedBrand.name}</h3>
                      <div className="flex items-center mt-1 space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {selectedBrand.category}
                        </Badge>
                        <span className="text-xs text-text-secondary flex items-center">
                          <Flower className="h-3 w-3 mr-1" />
                          {formatCount(selectedBrand.flawaCount)}
                        </span>
                      </div>
                    </div>
                    {!selectedBrandId && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="ml-auto"
                        onClick={() => setSelectedBrand(null)}
                        type="button"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label>Submission Type</Label>
                <RadioGroup defaultValue="text" className="flex flex-wrap gap-4" onValueChange={setSubmissionType}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="text" id="text" />
                    <Label htmlFor="text">Text</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="link" id="link" />
                    <Label htmlFor="link">Link (Tweet, Article)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="image" id="image" />
                    <Label htmlFor="image">Image</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="video" id="video" />
                    <Label htmlFor="video">Video Link</Label>
                  </div>
                </RadioGroup>
              </div>

              {submissionType === "text" && (
                <div className="space-y-2">
                  <Label htmlFor="content">Your Praise</Label>
                  <EnhancedTextarea
                    id="content"
                    ref={textareaRef}
                    placeholder="Share why you love this brand..."
                    className={`min-h-32 ${formErrors.message ? 'border-red-500' : ''}`}
                    maxLength={500}
                    value={messageContent}
                    onChange={(e) => setMessageContent(e.target.value)}
                    onEnhancedChange={(value) => setMessageContent(value)}
                    aria-describedby={formErrors.message ? "content-error" : "content-counter"}
                  />
                  {formErrors.message ? (
                    <p id="content-error" className="text-xs text-red-500">
                      {formErrors.message}
                    </p>
                  ) : (
                    <p id="content-counter" className={`text-xs ${messageContent.length > 480 ? 'text-amber-600' : 'text-muted-foreground'} text-right`}>
                      {messageContent.length}/500 characters
                    </p>
                  )}
                  {submissionType === "text" && messageContent && (
                    <div className="mt-4 p-4 border rounded-md bg-muted/50">
                      <Label className="text-xs text-muted-foreground">Preview:</Label>
                      <p className="text-sm whitespace-pre-wrap">{messageContent}</p>
                    </div>
                  )}
                </div>
              )}

              {submissionType === "link" && (
                <div className="space-y-2">
                  <Label htmlFor="link">Link URL</Label>
                  <div className="flex">
                    <div className="relative flex-1">
                      <LucideLink className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input id="link" placeholder="https://..." className="pl-8" />
                    </div>
                  </div>
                </div>
              )}

              {submissionType === "image" && (
                <div className="space-y-2">
                  <Label htmlFor="file">Upload Image</Label>
                  <div className="border-2 border-dashed border-border rounded-lg p-6 flex flex-col items-center justify-center">
                    <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-sm text-text-secondary mb-2">
                      Drag and drop your image here, or click to browse
                    </p>
                    <Input id="file" type="file" accept="image/*" className="hidden" />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => document.getElementById("file")?.click()}
                      type="button"
                    >
                      Browse Files
                    </Button>
                  </div>
                </div>
              )}

              {submissionType === "video" && (
                <div className="space-y-2">
                  <Label htmlFor="videoLink">Video Link</Label>
                  <div className="relative">
                    <LucideLink className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="videoLink"
                      placeholder="https://youtube.com/... or https://vimeo.com/..."
                      className="pl-8"
                    />
                  </div>
                  <p className="text-xs text-text-secondary">
                    Please provide a link to your video on YouTube, Vimeo, or other video platforms.
                  </p>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="name">Your Name</Label>
                <Input id="name" placeholder="How you want to be credited" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tags">Tags (separate with commas, max {MAX_TAGS})</Label>
                <div className="relative">
                  <Input
                    id="tags"
                    placeholder="e.g., customer service, product quality, innovation"
                    value={tagInput}
                    onChange={handleTagInputChange}
                    onKeyDown={handleTagInput}
                    onBlur={addTag}
                    disabled={tags.length >= MAX_TAGS}
                    aria-describedby="tags-description"
                  />
                </div>
                {tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {tags.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="flex items-center gap-1 bg-muted"
                      >
                        {tag}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => removeTag(tag)}
                          aria-label={`Remove tag ${tag}`}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
                <p id="tags-description" className="text-xs text-text-secondary">
                  Add relevant tags ({tags.length}/{MAX_TAGS}) to categorize your flawa. These help others find similar content.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Your Email (Optional)</Label>
                <Input id="email" type="email" placeholder="For notification when published" />
              </div>

              <div className="space-y-2">
                <Label>Your Social Media (Optional)</Label>
                <div className="space-y-3">
                  <div className="relative">
                    <div className="absolute left-2.5 top-2.5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-muted-foreground"
                      >
                        <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                      </svg>
                    </div>
                    <Input
                      placeholder="Twitter username"
                      className="pl-10"
                      value={socialLinks.twitter}
                      onChange={(e) => setSocialLinks({ ...socialLinks, twitter: e.target.value })}
                    />
                  </div>
                  <div className="relative">
                    <div className="absolute left-2.5 top-2.5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-muted-foreground"
                      >
                        <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                        <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                        <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                      </svg>
                    </div>
                    <Input
                      placeholder="Instagram username"
                      className="pl-10"
                      value={socialLinks.instagram}
                      onChange={(e) => setSocialLinks({ ...socialLinks, instagram: e.target.value })}
                    />
                  </div>
                  <div className="relative">
                    <div className="absolute left-2.5 top-2.5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-muted-foreground"
                      >
                        <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
                        <rect width="4" height="12" x="2" y="9" />
                        <circle cx="4" cy="4" r="2" />
                      </svg>
                    </div>
                    <Input
                      placeholder="LinkedIn username"
                      className="pl-10"
                      value={socialLinks.linkedin}
                      onChange={(e) => setSocialLinks({ ...socialLinks, linkedin: e.target.value })}
                    />
                  </div>
                </div>
              </div>

              <div className="text-sm text-text-secondary">
                <p>
                  By submitting, you acknowledge that Flawagram curates content from various online sources including
                  Product Hunt, Facebook, Twitter, and review websites.
                </p>
              </div>
            </form>
            <p className="text-xs text-muted-foreground text-center mt-4 px-6">
              Submissions are typically reviewed within 1-2 business days.
            </p>
          </CardContent>
          <CardFooter>
            <Button
              className="w-full flex items-center gap-2 bg-black text-white hover:bg-gray-800 border border-black"
              type="submit"
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Sending...</span>
                </>
              ) : (
                <>
                  <Flower className="h-4 w-4" />
                  <span>Send a Flawa</span>
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
