"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Bar<PERSON>hart,
  ChevronDown,
  CreditCard,
  Download,
  Eye,
  Flower,
  MoreHorizontal,
  Settings,
  Star,
  Trash,
  TrendingUp,
  Upload,
  Users,
  Link as LinkIcon,
  PieChart,
  Activity,
  Users2, // For User Management
  ShieldCheck, // For Admin Role
} from "lucide-react"
import Image from "next/image"
import { Flawa, User } from "@/types" // Import Flawa and User types
import { getAllFlawas, getTopFlawasByViews, getTopFlawasByLikes, getFlawasByBrand } from "@/data/flawas" // Import data functions
import { CSVUpload } from "@/components/admin/csv-upload"

// Mock current user role - in a real app, this would come from auth
type UserRole = "admin" | "brand"

export default function DashboardPage() {
  const [activeTab, setActiveTab] = useState("overview")
  const [currentUserRole, setCurrentUserRole] = useState<UserRole>("admin") // Default to admin for now
  const [allFlawas, setAllFlawas] = useState<Flawa[]>([])
  const [topFlawasByViews, setTopFlawasByViews] = useState<Flawa[]>([])
  const [topFlawasByLikes, setTopFlawasByLikes] = useState<Flawa[]>([])
  const [brandFlawas, setBrandFlawas] = useState<Flawa[]>([])
  const [totalPlatformViews, setTotalPlatformViews] = useState(0)
  const [totalPlatformLikes, setTotalPlatformLikes] = useState(0)

  // Mock user data for admin view
  const mockUsers: User[] = [
    { id: "user1", email: "<EMAIL>", name: "Admin User", role: "sitewideAdmin", avatarUrl: "https://i.pravatar.cc/150?u=admin", signupDate: "2023-01-15" },
    { id: "user2", email: "<EMAIL>", name: "Apple Manager", role: "pageAdmin", managedBrandIds: ["apple"], avatarUrl: "https://i.pravatar.cc/150?u=applemanager", signupDate: "2023-02-20" },
    { id: "user3", email: "<EMAIL>", name: "Regular User", role: "user", avatarUrl: "https://i.pravatar.cc/150?u=user", signupDate: "2023-03-10" },
    { id: "user4", email: "<EMAIL>", name: "Jane Doe", role: "user", avatarUrl: "https://i.pravatar.cc/150?u=janedoe", signupDate: "2023-04-05" },
  ];

  // Mock Flawa submissions count for users - in a real app, this would be dynamic
  const userFlawaCounts: Record<string, number> = {
    "user1": 5,
    "user2": 20,
    "user3": 2,
    "user4": 0,
  };


  useEffect(() => {
    const all = getAllFlawas();
    setAllFlawas(all);
    setTopFlawasByViews(getTopFlawasByViews(5)); // Get top 5 by views
    setTopFlawasByLikes(getTopFlawasByLikes(5)); // Get top 5 by likes

    if (currentUserRole === 'admin') {
      let totalViews = 0;
      let totalLikes = 0;
      all.forEach(flawa => {
        totalViews += flawa.views || 0;
        totalLikes += flawa.likes || 0;
      });
      setTotalPlatformViews(totalViews);
      setTotalPlatformLikes(totalLikes);
    } else if (currentUserRole === 'brand') {
      // Assuming the brand is 'apple' for this mock
      const brandSpecificFlawas = getFlawasByBrand("apple");
      setBrandFlawas(brandSpecificFlawas);
      // Brand specific stats will be updated below in the brand section
    }
  }, [currentUserRole]);


  // Mock brand data (can be dynamic based on logged-in brand user)
  const brand = {
    name: "Apple",
    logo: null,
    domain: "apple.com",
    description:
      "Apple Inc. is an American multinational technology company that designs, develops, and sells consumer electronics, computer software, and online services.",
    website: "https://apple.com",
    category: "Technology",
    flawaCount: 1243,
    tier: "Pro",
    stats: { // These stats are for a specific brand, will adjust for admin view
      views: currentUserRole === 'brand' ? 12543 : totalPlatformViews, // Example: show platform views for admin
      likes: currentUserRole === 'brand' ? 580 : totalPlatformLikes, // Example: show platform likes for admin
      engagement: 3.2, // Engagement might be calculated differently for admin
      flawasThisMonth: 42,
      viewsChange: 12,
      engagementChange: 0.3,
      flawasChange: 8,
    },
  }

  const recentFlawas: Flawa[] = [ // Make this Flawa[]
    {
      id: "1",
      type: "text",
      content:
        "I've been using Apple products for over a decade now and I'm still amazed by how seamless the ecosystem is.",
      author: "Sarah Johnson",
      brand: "Apple", // Added brand
      brandDomain: "apple.com", // Added brandDomain
      timestamp: "2023-05-15T10:00:00Z", // Changed date to timestamp
      status: "approved",
      likes: 42,
      views: 150,
    },
    {
      id: "2",
      type: "text",
      content: "Just got my new MacBook Pro and I'm blown away by the performance. Apple silicon is a game changer!",
      author: "Tech Enthusiast",
      brand: "Apple",
      brandDomain: "apple.com",
      timestamp: "2023-05-12T11:00:00Z",
      status: "approved",
      likes: 78,
      views: 230,
    },
    {
      id: "3",
      type: "text",
      content: "Apple's customer service is unmatched. Had an issue with my iPhone and they resolved it immediately.",
      author: "Loyal Customer",
      brand: "Apple",
      brandDomain: "apple.com",
      timestamp: "2023-05-10T12:00:00Z",
      status: "pending",
      likes: 0,
      views: 10,
    },
    {
      id: "4",
      type: "text",
      content: "The attention to detail in Apple products is incredible. Every aspect is thoughtfully designed.",
      author: "Design Lover",
      brand: "Apple",
      brandDomain: "apple.com",
      timestamp: "2023-05-08T13:00:00Z",
      status: "approved",
      likes: 92,
      views: 310,
    },
    {
      id: "5",
      type: "text",
      content: "Apple's commitment to privacy is why I trust them with my data. No other tech company comes close.",
      author: "Privacy Advocate",
      brand: "Apple",
      brandDomain: "apple.com",
      timestamp: "2023-05-05T14:00:00Z",
      status: "approved",
      likes: 54,
      views: 180,
    },
  ]

  const audienceInsights = {
    demographics: [
      { ageGroup: "18-24", percentage: 25 },
      { ageGroup: "25-34", percentage: 35 },
      { ageGroup: "35-44", percentage: 20 },
      { ageGroup: "45+", percentage: 20 },
    ],
    locations: [
      { country: "USA", percentage: 60 },
      { country: "Canada", percentage: 15 },
      { country: "UK", percentage: 10 },
      { country: "Other", percentage: 15 },
    ],
  }

  const referralSources = [
    { source: "Direct", views: 5000, percentage: 40 },
    { source: "Google", views: 3500, percentage: 28 },
    { source: "Social Media", views: 2500, percentage: 20 },
    { source: "Other", views: 1543, percentage: 12 },
  ]

  // Get favicon URL as fallback for brand logo
  const getFaviconUrl = (domain: string, size = 128): string => {
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
  }

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1 bg-muted/30">
        <div className="container px-4 md:px-6 py-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
            <div className="flex items-center gap-4">
              <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-16 h-16 shadow-card">
                <Image
                  src={brand.logo || getFaviconUrl(brand.domain)}
                  alt={brand.name}
                  width={64}
                  height={64}
                  className="brand-logo"
                />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h1 className="text-2xl font-bold">{currentUserRole === 'admin' ? "Admin Dashboard" : brand.name}</h1>
                  {currentUserRole === 'brand' && <Badge>{brand.tier}</Badge>}
                  {currentUserRole === 'admin' && <Badge variant="destructive"><ShieldCheck className="w-3 h-3 mr-1 inline-block"/>Admin</Badge>}
                </div>
                <p className="text-text-secondary">{currentUserRole === 'admin' ? "Platform Overview" : brand.domain}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {/* Role switcher for demo purposes */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    Switch Role ({currentUserRole}) <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setCurrentUserRole("admin")}>Admin</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCurrentUserRole("brand")}>Brand (Apple)</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {currentUserRole === 'brand' && (
                <>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <Settings className="h-4 w-4" />
                    <span>Settings</span>
                  </Button>
                  <Button size="sm" className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    <span>View Public Page</span>
                  </Button>
                </>
              )}
            </div>
          </div>

          <Tabs defaultValue="overview" className="space-y-4" onValueChange={setActiveTab}>
            <TabsList className={`grid w-full ${currentUserRole === 'admin' ? 'grid-cols-3 md:grid-cols-5' : 'grid-cols-4 md:grid-cols-5 lg:grid-cols-6'}`}>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              {currentUserRole === 'admin' && <TabsTrigger value="userManagement">User Management</TabsTrigger>}
              {currentUserRole === 'admin' && <TabsTrigger value="bulkOps">Bulk Operations</TabsTrigger>}
              <TabsTrigger value="flawas">{currentUserRole === 'admin' ? "All Flawas" : "Flawas"}</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              {currentUserRole === 'brand' && (
                <>
                  <TabsTrigger value="profile">Profile</TabsTrigger>
                  <TabsTrigger value="billing" className="hidden md:block">Billing</TabsTrigger>
                  <TabsTrigger value="settings" className="hidden lg:block">Settings</TabsTrigger>
                </>
              )}
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              {/* General Stats Cards */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {currentUserRole === 'admin' ? "Total Platform Views" : "Total Views"}
                    </CardTitle>
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {currentUserRole === 'admin' ? totalPlatformViews.toLocaleString() : brand.stats.views.toLocaleString()}
                    </div>
                    {currentUserRole === 'brand' && (
                      <p className="text-xs text-muted-foreground">
                        {brand.stats.viewsChange > 0 ? "+" : ""}
                        {brand.stats.viewsChange}% from last month
                      </p>
                    )}
                     {currentUserRole === 'admin' && (
                      <p className="text-xs text-muted-foreground">Across all flawas on the platform.</p>
                    )}
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                       {currentUserRole === 'admin' ? "Total Platform Likes" : "Engagement Rate"}
                    </CardTitle>
                    {currentUserRole === 'admin' ? <Flower className="h-4 w-4 text-muted-foreground" /> : <TrendingUp className="h-4 w-4 text-muted-foreground" />}
                  </CardHeader>
                  <CardContent>
                     <div className="text-2xl font-bold">
                      {currentUserRole === 'admin' ? totalPlatformLikes.toLocaleString() : `${brand.stats.engagement}%`}
                    </div>
                    {currentUserRole === 'brand' && (
                      <p className="text-xs text-muted-foreground">
                        {brand.stats.engagementChange > 0 ? "+" : ""}
                        {brand.stats.engagementChange}% from last month
                      </p>
                    )}
                    {currentUserRole === 'admin' && (
                      <p className="text-xs text-muted-foreground">Total likes on all flawas.</p>
                    )}
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {currentUserRole === 'admin' ? "Total Flawas" : "New Flawas"}
                    </CardTitle>
                    <Flower className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                       {currentUserRole === 'admin' ? allFlawas.length.toLocaleString() : brand.stats.flawasThisMonth}
                    </div>
                    {currentUserRole === 'brand' && (
                      <p className="text-xs text-muted-foreground">
                        {brand.stats.flawasChange > 0 ? "+" : ""}
                        {brand.stats.flawasChange} from last month
                      </p>
                    )}
                     {currentUserRole === 'admin' && (
                      <p className="text-xs text-muted-foreground">Total flawas submitted on the platform.</p>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Admin Specific: Top Flawas */}
              {currentUserRole === 'admin' && (
                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Top 5 Flawas by Views</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-[50px]">#</TableHead>
                            <TableHead>Content</TableHead>
                            <TableHead>Brand</TableHead>
                            <TableHead className="text-right">Views</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {topFlawasByViews.map((flawa, index) => (
                            <TableRow key={flawa.id}>
                              <TableCell>{index + 1}</TableCell>
                              <TableCell className="max-w-[200px] truncate">{flawa.content}</TableCell>
                              <TableCell>{flawa.brand}</TableCell>
                              <TableCell className="text-right">{flawa.views?.toLocaleString() || 0}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle>Top 5 Flawas by Likes</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-[50px]">#</TableHead>
                            <TableHead>Content</TableHead>
                            <TableHead>Brand</TableHead>
                            <TableHead className="text-right">Likes</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {topFlawasByLikes.map((flawa, index) => (
                            <TableRow key={flawa.id}>
                              <TableCell>{index + 1}</TableCell>
                              <TableCell className="max-w-[200px] truncate">{flawa.content}</TableCell>
                              <TableCell>{flawa.brand}</TableCell>
                              <TableCell className="text-right">{flawa.likes.toLocaleString()}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Brand Specific: Recent Flawas */}
              {currentUserRole === 'brand' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Flawas</CardTitle>
                    <CardDescription>Your most recent flawas and their status.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Content</TableHead>
                          <TableHead>Author</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Views</TableHead> {/* Added Views */}
                          <TableHead>Likes</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {recentFlawas.map((flawa) => ( // Assuming recentFlawas is for the current brand
                          <TableRow key={flawa.id}>
                            <TableCell className="max-w-[300px] truncate">{flawa.content}</TableCell>
                            <TableCell>{flawa.author}</TableCell>
                            <TableCell>{flawa.timestamp ? new Date(flawa.timestamp).toLocaleDateString() : "N/A"}</TableCell>
                            <TableCell>{flawa.views?.toLocaleString() || 0}</TableCell> {/* Display Views */}
                            <TableCell>{flawa.likes.toLocaleString()}</TableCell>
                            <TableCell>
                              <Badge variant={flawa.status === "approved" ? "default" : "outline"}>
                                {flawa.status === "approved" ? "Approved" : "Pending"}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    <span>View</span>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Star className="mr-2 h-4 w-4" />
                                    <span>Feature</span>
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem>
                                    <Trash className="mr-2 h-4 w-4" />
                                    <span>Hide</span>
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline">Previous</Button>
                    <Button variant="outline">Next</Button>
                  </CardFooter>
                </Card>
              )}
            </TabsContent>

            {currentUserRole === 'admin' && (
              <TabsContent value="userManagement" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users2 className="h-5 w-5 text-muted-foreground" />
                      User Management
                    </CardTitle>
                    <CardDescription>View and manage platform users.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Avatar</TableHead>
                          <TableHead>Name</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>Role</TableHead>
                          <TableHead>Flawas Submitted</TableHead>
                          <TableHead>Signup Date</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mockUsers.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>
                              <Image
                                src={user.avatarUrl || getFaviconUrl(user.email.split('@')[1] || 'example.com', 32)}
                                alt={user.name || user.email}
                                width={32}
                                height={32}
                                className="rounded-full"
                              />
                            </TableCell>
                            <TableCell>{user.name || "N/A"}</TableCell>
                            <TableCell>{user.email}</TableCell>
                            <TableCell><Badge variant={user.role === 'sitewideAdmin' ? "destructive" : "secondary"}>{user.role}</Badge></TableCell>
                            <TableCell>{userFlawaCounts[user.id] !== undefined ? userFlawaCounts[user.id] : 'N/A'}</TableCell>
                            <TableCell>{user.signupDate ? new Date(user.signupDate).toLocaleDateString() : "N/A"}</TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>View Profile</DropdownMenuItem>
                                  <DropdownMenuItem>Edit Role</DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-red-600">Suspend User</DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>
            )}

            {currentUserRole === 'admin' && (
              <TabsContent value="bulkOps" className="space-y-4">
                <CSVUpload />
              </TabsContent>
            )}

            <TabsContent value="flawas" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>{currentUserRole === 'admin' ? "All Platform Flawas" : "All Flawas"}</CardTitle>
                  <CardDescription>
                    {currentUserRole === 'admin' ? "Manage all flawas across the platform." : "Manage all flawas for your brand."}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <Input placeholder="Search flawas..." className="w-[250px]" />
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" className="flex items-center gap-1">
                            <span>Filter</span>
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>All</DropdownMenuItem>
                          <DropdownMenuItem>Approved</DropdownMenuItem>
                          <DropdownMenuItem>Pending</DropdownMenuItem>
                          <DropdownMenuItem>Hidden</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <Button className="flex items-center gap-1">
                      <Download className="h-4 w-4" />
                      <span>Export</span>
                    </Button>
                  </div>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Content</TableHead>
                        <TableHead>Author</TableHead>
                        <TableHead>Date</TableHead>
                        {currentUserRole === 'admin' && <TableHead>Brand</TableHead>}
                        <TableHead>Views</TableHead>
                        <TableHead>Likes</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {(currentUserRole === 'admin' ? allFlawas : brandFlawas.length > 0 ? brandFlawas : recentFlawas).map((flawa) => ( // Use allFlawas for admin, brandFlawas or fallback to recentFlawas for brand
                        <TableRow key={flawa.id}>
                          <TableCell className="max-w-[300px] truncate">{flawa.content}</TableCell>
                          <TableCell>{flawa.author}</TableCell>
                          <TableCell>{flawa.timestamp ? new Date(flawa.timestamp).toLocaleDateString() : "N/A"}</TableCell>
                          {currentUserRole === 'admin' && <TableCell>{flawa.brand}</TableCell>}
                          <TableCell>{flawa.views?.toLocaleString() || 0}</TableCell>
                          <TableCell>{flawa.likes.toLocaleString()}</TableCell>
                          <TableCell>
                            <Badge variant={(flawa.status || "approved") === "approved" ? "default" : "outline"}>
                              {(flawa.status || "approved") === "approved" ? "Approved" : "Pending"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Star className="mr-2 h-4 w-4" />
                                  <span>Feature</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Trash className="mr-2 h-4 w-4" />
                                  <span>Hide</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <div className="text-sm text-muted-foreground">Showing 5 of 42 flawas</div>
                  <div className="flex gap-2">
                    <Button variant="outline">Previous</Button>
                    <Button variant="outline">Next</Button>
                  </div>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-muted-foreground" />
                      Audience Insights
                    </CardTitle>
                    <CardDescription>Understand who is viewing your page and flawas.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Demographics (Age Group)</h4>
                      <ul className="space-y-1 text-sm text-muted-foreground">
                        {audienceInsights.demographics.map((demo) => (
                          <li key={demo.ageGroup} className="flex justify-between">
                            <span>{demo.ageGroup}</span>
                            <span>{demo.percentage}%</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <Separator />
                    <div>
                      <h4 className="font-medium mb-2">Top Locations</h4>
                      <ul className="space-y-1 text-sm text-muted-foreground">
                        {audienceInsights.locations.map((loc) => (
                          <li key={loc.country} className="flex justify-between">
                            <span>{loc.country}</span>
                            <span>{loc.percentage}%</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <LinkIcon className="h-5 w-5 text-muted-foreground" />
                      Referral Sources
                    </CardTitle>
                    <CardDescription>How users are finding your brand page.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      {referralSources.map((source) => (
                        <li key={source.source} className="flex justify-between items-center">
                          <span>{source.source}</span>
                          <div className="text-right">
                            <div>{source.views.toLocaleString()} views</div>
                            <div className="text-xs">({source.percentage}%)</div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChart className="h-5 w-5 text-muted-foreground" />
                    Comparative Analytics
                  </CardTitle>
                  <CardDescription>
                    How you perform against anonymized averages for similar brands or categories. (Coming Soon)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[200px] flex items-center justify-center border rounded-md">
                    <p className="text-sm text-muted-foreground">Comparative analytics will be shown here.</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-muted-foreground" />
                    Impact of Featured Flawas
                  </CardTitle>
                  <CardDescription>
                    Does featuring a flawa measurably increase views or engagement? (Coming Soon)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[200px] flex items-center justify-center border rounded-md">
                    <p className="text-sm text-muted-foreground">Analysis of featured flawa impact will be shown here.</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="profile" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Brand Profile</CardTitle>
                  <CardDescription>Update your brand information and public profile.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Brand Name</Label>
                    <Input id="name" defaultValue={brand.name} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" defaultValue={brand.description} className="min-h-32" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input id="website" defaultValue={brand.website} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <Input id="category" defaultValue={brand.category} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="logo">Logo</Label>
                    <div className="flex items-center gap-4">
                      <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-16 h-16 shadow-card">
                        <Image
                          src={brand.logo || getFaviconUrl(brand.domain)}
                          alt={brand.name}
                          width={64}
                          height={64}
                          className="brand-logo"
                        />
                      </div>
                      <Button variant="outline" className="flex items-center gap-1">
                        <Upload className="h-4 w-4" />
                        <span>Upload New Logo</span>
                      </Button>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button>Save Changes</Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="billing" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Billing & Subscription</CardTitle>
                  <CardDescription>Manage your subscription and billing information.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="rounded-lg border p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="font-medium">Current Plan</h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge>Pro</Badge>
                          <span className="text-sm text-muted-foreground">$49/month</span>
                        </div>
                      </div>
                      <Button variant="outline">Change Plan</Button>
                    </div>
                  </div>
                  <div className="rounded-lg border p-4">
                    <h3 className="font-medium mb-2">Payment Method</h3>
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      <span>•••• •••• •••• 4242</span>
                      <Badge variant="outline" className="ml-auto">
                        Expires 12/25
                      </Badge>
                    </div>
                  </div>
                  <div className="rounded-lg border p-4">
                    <h3 className="font-medium mb-2">Billing History</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm font-medium">May 1, 2023</p>
                          <p className="text-xs text-muted-foreground">Pro Plan - Monthly</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">$49.00</p>
                          <Badge variant="outline" className="text-xs">
                            Paid
                          </Badge>
                        </div>
                      </div>
                      <Separator />
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm font-medium">Apr 1, 2023</p>
                          <p className="text-xs text-muted-foreground">Pro Plan - Monthly</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">$49.00</p>
                          <Badge variant="outline" className="text-xs">
                            Paid
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Settings</CardTitle>
                  <CardDescription>Manage your account settings and preferences.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Email Notifications</h3>
                        <p className="text-sm text-muted-foreground">Receive email notifications for new flawas.</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <Separator />
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Auto-Approve Flawas</h3>
                        <p className="text-sm text-muted-foreground">Automatically approve new flawas.</p>
                      </div>
                      <Switch />
                    </div>
                    <Separator />
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Public Profile</h3>
                        <p className="text-sm text-muted-foreground">Make your brand profile visible to the public.</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button>Save Settings</Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
