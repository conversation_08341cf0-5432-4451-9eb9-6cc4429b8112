"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Flower } from "lucide-react";

const formSchema = z.object({
  brandName: z.string().min(1, "Brand name is required (e.g., Acme Corp)"),
  onlinePresence: z.string().optional(),
  isPhysicalBusiness: z.boolean().default(false),
  fullName: z.string().min(1, "Your full name is required"),
  workEmail: z.string().email("Please enter a valid work email address"),
  role: z.string().min(1, "Please select your role"),
  roleOther: z.string().optional(),
  howHeard: z.string().min(1, "Please let us know how you heard about us"),
  howHeardOther: z.string().optional(),
  businessAddress: z.string().optional(),
  businessCategory: z.string().optional(),
  businessCategoryOther: z.string().optional(),
  storefrontImage: z.any().optional(),
  optionalSocialLink: z.string().url("Please enter a valid URL (e.g., https://facebook.com/yourpage)").optional().or(z.literal('')),
})
.superRefine((data, ctx) => {
  if (!data.isPhysicalBusiness) {
    if (!data.onlinePresence || data.onlinePresence.trim() === "") {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["onlinePresence"],
        message: "Main online presence is required if not a physical business.",
      });
    } else {
      try {
        new URL(data.onlinePresence);
      } catch (_) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["onlinePresence"],
          message: "Please enter a valid URL (e.g., https://example.com)",
        });
      }
    }
  }

  if (data.role === "Other" && (!data.roleOther || data.roleOther.trim() === "")) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ["roleOther"],
      message: "Please specify your role",
    });
  }
  if (data.howHeard === "Other" && (!data.howHeardOther || data.howHeardOther.trim() === "")) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ["howHeardOther"],
      message: "Please specify how you heard about us",
    });
  }

  if (data.isPhysicalBusiness) {
    if (!data.businessAddress || data.businessAddress.trim() === "") {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["businessAddress"],
        message: "Business address is required for physical businesses",
      });
    }
    if (!data.businessCategory || data.businessCategory.trim() === "") {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["businessCategory"],
        message: "Business category is required for physical businesses",
      });
    }
    if (data.businessCategory === "Other" && (!data.businessCategoryOther || data.businessCategoryOther.trim() === "")) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["businessCategoryOther"],
        message: "Please specify your business category",
      });
    }
  }
});

type FormData = z.infer<typeof formSchema>;

const roles = [
  "Founder / CEO",
  "CFO / Accountant",
  "Marketing Manager",
  "CTO / Tech Lead",
  "Sales Manager",
  "Customer Support",
  "Other",
];

const howHeardOptions = [
  "Twitter/X",
  "Word of Mouth",
  "Saw a Flawagram",
  "Referred by a Customer",
  "News Article",
  "Other",
];

const businessCategories = [
  "Retail Shop",
  "Salon / Barber",
  "Tailor",
  "Mechanic / Auto",
  "Food Business / Bakery",
  "Other",
];

export default function CreateBrandPage() {
  const [submissionStatus, setSubmissionStatus] = useState<"idle" | "submitting" | "success_auto" | "success_review">("idle");

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      brandName: "",
      onlinePresence: "",
      isPhysicalBusiness: false,
      fullName: "",
      workEmail: "",
      role: "",
      roleOther: "",
      howHeard: "",
      howHeardOther: "",
      businessAddress: "",
      businessCategory: "",
      businessCategoryOther: "",
      storefrontImage: undefined,
      optionalSocialLink: "",
    },
  });

  const watchedIsPhysical = form.watch("isPhysicalBusiness");
  const watchedRole = form.watch("role");
  const watchedHowHeard = form.watch("howHeard");
  const watchedBusinessCategory = form.watch("businessCategory");

  async function onSubmit(data: FormData) {
    setSubmissionStatus("submitting");
    console.log("Form data submitted:", data);

    await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API call

    try {
      const emailDomain = data.workEmail.split('@')[1];
      let onlinePresenceDomain = null;
      if (data.onlinePresence && data.onlinePresence.trim() !== "") {
        try {
            const url = new URL(data.onlinePresence);
            onlinePresenceDomain = url.hostname.replace(/^www\./, '');
        } catch (e) {
            console.error("Invalid online presence URL for domain extraction:", data.onlinePresence);
        }
      }

      if (!data.isPhysicalBusiness && onlinePresenceDomain && emailDomain && emailDomain.toLowerCase() === onlinePresenceDomain.toLowerCase()) {
        setSubmissionStatus("success_auto");
      } else {
        setSubmissionStatus("success_review");
      }
    } catch (error) {
        console.error("Error processing submission:", error);
        setSubmissionStatus("idle");
    }
  }

  if (submissionStatus === "success_auto") {
    return (
      <div className="container mx-auto max-w-2xl py-12">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center text-green-600">🎉 Submission Successful!</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-lg">Your Flawagram brand page has been created!</p>
            <p className="mt-2">We've sent an email to your work address with the link to your Flawagram page.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (submissionStatus === "success_review") {
    return (
      <div className="container mx-auto max-w-2xl py-12">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center text-blue-600">👍 Submission Received!</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-lg">Thanks! We’re reviewing your submission.</p>
            <p className="mt-2">You’ll receive your Flawagram link soon via email.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-2xl py-8 px-4 sm:px-6 lg:px-8">
      <Card className="shadow-xl">
        <CardHeader className="bg-gray-50 dark:bg-gray-800 rounded-t-lg">
          <CardTitle className="text-3xl font-bold tracking-tight text-center text-gray-900 dark:text-white flex items-center justify-center">
            <Flower className="h-8 w-8 mr-2 text-pink-500" /> Create Your Flawagram Brand Page
          </CardTitle>
          <CardDescription className="text-center text-gray-600 dark:text-gray-300 mt-1">
            Tell us about your brand to get started.
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-6 space-y-8">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Section 1: Brand Information */}
              <section className="space-y-6 p-6 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800/30 shadow-sm">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 border-b pb-2 mb-4">1. Brand Information</h2>
                <FormField
                  control={form.control}
                  name="isPhysicalBusiness"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 shadow-sm bg-gray-50 dark:bg-gray-700/50 mb-6"> {/* Added mb-6 for spacing */}
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          I don’t have a website or online page. I run a physical business.
                        </FormLabel>
                        <FormDescription>
                          Checking this will reveal physical business fields and make online presence optional.
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="brandName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Brand Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Acme Innovations" {...field} />
                      </FormControl>
                      {/* Placeholder for async validation message */}
                      {/* <FormDescription>Checking brand name...</FormDescription> */}
                      {/* <FormMessage className="text-yellow-600">A similar brand name already exists or is pending review.</FormMessage> */}
                      <FormDescription>
                        No need to add Ltd, Inc, Enterprises, etc.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {!watchedIsPhysical && (
                  <FormField
                    control={form.control}
                    name="onlinePresence"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Main Online Presence (Website, Facebook, or Instagram)</FormLabel>
                        <FormControl>
                          <Input type="url" placeholder="https://yourbrand.com or https://instagram.com/yourbrand" {...field} />
                        </FormControl>
                        <FormDescription>
                          Paste the main link your customers use to find you.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </section>

              {/* Section 2: Your Details */}
              <section className="space-y-6 p-6 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800/30 shadow-sm">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 border-b pb-2 mb-4">2. Your Details</h2>
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Your Full Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Jane Doe" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="workEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Work Email Address</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormDescription>
                        Using an email address with your brand's domain (e.g., <EMAIL>) can help with automatic approval.
                        Other email addresses are welcome but may require manual review.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Your Role</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select your role" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {roles.map((roleItem) => (
                            <SelectItem key={roleItem} value={roleItem}>
                              {roleItem}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {watchedRole === "Other" && (
                  <FormField
                    control={form.control}
                    name="roleOther"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Please specify your role</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Operations Manager" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="howHeard"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>How Did You Hear About Flawagram?</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select an option" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {howHeardOptions.map((option) => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {watchedHowHeard === "Other" && (
                  <FormField
                    control={form.control}
                    name="howHeardOther"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Please specify</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., LinkedIn post" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </section>

              {/* Section 3: Physical Business Details (Conditional) */}
              {watchedIsPhysical && (
                <section className="space-y-6 p-6 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800/30 shadow-sm">
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 border-b pb-2 mb-4">3. Physical Business Details</h2>
                  <FormField
                    control={form.control}
                    name="businessAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Business Address</FormLabel>
                        <FormControl>
                          <Textarea placeholder="123 Main St, Anytown, USA" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="businessCategory"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Business Category</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {businessCategories.map((category) => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {watchedBusinessCategory === "Other" && (
                    <FormField
                      control={form.control}
                      name="businessCategoryOther"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Please specify category</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., Pet Grooming" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                  <FormField
                    control={form.control}
                    name="storefrontImage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Upload an Image (Optional)</FormLabel>
                        <FormControl>
                          <Input 
                            type="file" 
                            accept="image/*" 
                            onChange={(e) => field.onChange(e.target.files ? e.target.files[0] : null)}
                          />
                        </FormControl>
                        <FormDescription>
                          Photo of your storefront, signage, or product.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="optionalSocialLink"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Optional Facebook or Instagram Link</FormLabel>
                        <FormControl>
                          <Input type="url" placeholder="https://facebook.com/yourbrand" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </section>
              )}

              {/* Section 4: Submit */}
              <CardFooter className="flex justify-center pt-8">
                <Button 
                  type="submit" 
                  className="w-full max-w-xs text-lg py-3"
                  disabled={submissionStatus === 'submitting'}
                >
                  {submissionStatus === 'submitting' ? 'Submitting...' : 'Create My Flawagram Brand Page'}
                </Button>
              </CardFooter>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}