"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import Image from "next/image"
import Link from "next/link"
import { Flower, Globe, ShieldCheck, Share2 } from "lucide-react"
import { brands } from "@/data/brands"
import { getFlawasByBrand, getFeaturedFlawas, getRecentFlawas } from "@/data/flawas"
import { notFound } from "next/navigation"
import { useState, use } from "react"
import { FlawaCard } from "@/components/ui/flawa-card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { SubmissionForm } from "@/components/submission-form"
import { toast } from "sonner"

// Format flawa count (e.g., 1000 -> 1K)
const formatCount = (count: number): string => {
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1).replace(/\.0$/, "")}K`
  }
  return count.toString()
}

// Get favicon URL as fallback for brand logo
const getFaviconUrl = (domain: string, size = 128): string => {
  return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`
}

export default function BrandPage({ params: paramsPromise }: { params: Promise<{ brandId: string }> }) {
  const params = use(paramsPromise)
  const brandIdFromParams = params.brandId.toLowerCase(); // Normalize brandId to lowercase
  const [activeFilter, setActiveFilter] = useState<string | null>(null)
  const [flawasToShow, setFlawasToShow] = useState(6)

  // Get the brand data based on the id parameter
  const brand = brands[brandIdFromParams]

  // If the brand doesn't exist, show 404
  if (!brand) {
    notFound()
  }

  // Get flawas for this brand
  const allFlawas = getFlawasByBrand(brandIdFromParams)
  const featuredFlawas = getFeaturedFlawas(brandIdFromParams)
  const recentFlawas = getRecentFlawas(brandIdFromParams)

  const handleShare = async () => {
    const shareUrl = window.location.href;
    const shareTitle = `Check out ${brand.name} on Flawagram!`;
    const shareText = `Discover ${brand.name}: ${brand.description?.substring(0,100)}...`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: shareTitle,
          text: shareText,
          url: shareUrl,
        });
        toast.success("Brand link shared!");
      } catch (error) {
        console.error("Error sharing:", error);
        // Fallback to copy if user cancels share dialog or API fails
        navigator.clipboard.writeText(shareUrl)
          .then(() => toast.success("Link copied to clipboard!"))
          .catch(err => {
            console.error("Failed to copy:", err)
            toast.error("Failed to copy link.")
          });
      }
    } else {
      // Fallback for browsers that do not support navigator.share
      navigator.clipboard.writeText(shareUrl)
        .then(() => toast.success("Link copied to clipboard!"))
        .catch(err => {
            console.error("Failed to copy:", err)
            toast.error("Failed to copy link.")
        });
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        <section className="w-full py-12 md:py-24 bg-muted">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col md:flex-row gap-8 items-center md:items-start">
              <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-32 h-32 shadow-card">
                <Image
                  src={brand.logo || getFaviconUrl(brand.domain)}
                  alt={brand.name}
                  width={120}
                  height={120}
                  className="brand-logo"
                />
              </div>
              <div className="flex-1 text-center md:text-left">
                <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
                  <h1 className="text-3xl font-bold">{brand.name}</h1>
                  {!brand.isClaimed && (
                    <Badge variant="outline" className="self-center md:self-auto bg-yellow-50 text-yellow-800 border-yellow-300">
                      Unclaimed
                    </Badge>
                  )}
                </div>
                <p className="mt-2 text-text-secondary max-w-2xl">{brand.description}</p>
                <div className="flex flex-wrap items-center gap-4 mt-4 justify-center md:justify-start">
                  <div className="flex items-center">
                    <Badge variant="outline" className="text-xs">
                      {brand.category}
                    </Badge>
                  </div>
                  <div
                    className="flex items-center text-text-secondary text-sm"
                    title={`${formatCount(brand.flawaCount)} Flawas received`}
                  >
                    <Flower className="h-4 w-4 mr-1" />
                    <span>{formatCount(brand.flawaCount)}</span>
                  </div>
                  <a
                    href={brand.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-text-secondary hover:text-hover transition-colors flex items-center"
                    title={`Visit ${brand.name}'s website`}
                  >
                    <Globe className="h-4 w-4" />
                  </a>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleShare}
                    className="text-text-secondary hover:text-hover h-auto p-0"
                    title={`Share ${brand.name}'s Flawagram page`}
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
                {/* Action Buttons for Brand Page */}
                <div className="mt-6 flex flex-col sm:flex-row gap-3 justify-center md:justify-start">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button className="flex gap-2 w-full sm:w-auto">
                        <Flower className="h-4 w-4" />
                        <span>Send a Flawa</span>
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Send a Flawa</DialogTitle>
                        <DialogDescription>Share your love for {brand.name}. No account required.</DialogDescription>
                      </DialogHeader>
                      <SubmissionForm inDialog={true} selectedBrandId={brand.id} />
                    </DialogContent>
                  </Dialog>
                  {!brand.isClaimed && (
                    <Link href={`/claim?brandId=${brand.id}&brandName=${encodeURIComponent(brand.name)}`} passHref>
                      <Button variant="outline" className="flex gap-2 w-full sm:w-auto">
                        <ShieldCheck className="h-4 w-4" />
                        <span>Claim this Brand</span>
                      </Button>
                    </Link>
                  )}
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className="w-full py-12">
          <div className="container px-4 md:px-6">
            <Tabs defaultValue="all" className="w-full">
              <div className="flex justify-between items-center mb-8">
                <TabsList>
                  <TabsTrigger value="all">All Flawas</TabsTrigger>
                  <TabsTrigger value="featured">Featured</TabsTrigger>
                  <TabsTrigger value="recent">Recent</TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="all" className="mt-0">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {allFlawas.slice(0, flawasToShow).map((flawa) => (
                    <FlawaCard key={flawa.id} flawa={flawa} variant="brand-page" />
                  ))}
                </div>
                {flawasToShow < allFlawas.length && (
                  <div className="flex justify-center mt-8">
                    <Button
                      variant="outline"
                      onClick={() => setFlawasToShow(prev => prev + 6)}
                    >
                      Load More
                    </Button>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="featured" className="mt-0">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {featuredFlawas.map((flawa) => (
                    <FlawaCard key={flawa.id} flawa={flawa} variant="brand-page" />
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="recent" className="mt-0">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {recentFlawas.map((flawa) => (
                    <FlawaCard key={flawa.id} flawa={flawa} variant="brand-page" />
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>
      </main>
    </div>
  )
} 