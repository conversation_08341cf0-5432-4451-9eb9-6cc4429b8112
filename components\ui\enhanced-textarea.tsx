"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON><PERSON>, RotateCcw, Loader2 } from "lucide-react"
import { enhanceMessageWithEmojis } from "@/lib/gemini"
import { toast } from "sonner"

export interface EnhancedTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  maxLength?: number;
  onEnhancedChange?: (value: string) => void;
}

const EnhancedTextarea = React.forwardRef<
  HTMLTextAreaElement,
  EnhancedTextareaProps
>(({ className, maxLength = 280, onEnhancedChange, ...props }, ref) => {
  const [originalText, setOriginalText] = React.useState<string>("")
  const [isEnhanced, setIsEnhanced] = React.useState<boolean>(false)
  const [isLoading, setIsLoading] = React.useState<boolean>(false)
  const [error, setError] = React.useState<string | null>(null)

  // Handle the enhancement process
  const handleEnhance = async () => {
    // Get the current value from the textarea
    const currentValue = (ref as React.RefObject<HTMLTextAreaElement>)?.current?.value || props.value as string || "";

    // Save the original text
    setOriginalText(currentValue);

    // Don't enhance if empty
    if (!currentValue.trim()) {
      setError("Please enter some text before enhancing");
      toast.error("Please enter some text before enhancing");
      return;
    }

    // Start loading
    setIsLoading(true);
    setError(null);

    try {
      // Call the Gemini API to enhance the message
      const response = await enhanceMessageWithEmojis(currentValue, maxLength);

      if (response.error) {
        setError(response.error);
        toast.error(response.error || "Failed to enhance message");
        setIsLoading(false);
        return;
      }

      // Update the textarea with the enhanced text
      if (ref && 'current' in ref && ref.current) {
        ref.current.value = response.text;
      }

      // Call the onChange handler if provided
      if (onEnhancedChange) {
        onEnhancedChange(response.text);
      }

      // Mark as enhanced
      setIsEnhanced(true);

      // Show success toast
      toast.success("Message enhanced with AI magic ✨");
    } catch (err) {
      setError("Failed to enhance message");
      toast.error("Failed to enhance message");
      console.error("Enhancement error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle reverting to the original text
  const handleRevert = () => {
    // Update the textarea with the original text
    if (ref && 'current' in ref && ref.current) {
      ref.current.value = originalText;
    }

    // Call the onChange handler if provided
    if (onEnhancedChange) {
      onEnhancedChange(originalText);
    }

    // Reset the enhanced state
    setIsEnhanced(false);

    // Show toast notification
    toast.info("Reverted to original message");
  };

  return (
    <div className="relative">
      <textarea
        className={cn(
          "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
          className
        )}
        ref={ref}
        {...props}
      />

      {/* Enhancement button */}
      <div className="absolute right-3 bottom-3">
        {isLoading ? (
          <button
            type="button"
            className="text-muted-foreground cursor-not-allowed"
            disabled
          >
            <Loader2 className="h-4 w-4 animate-spin" />
          </button>
        ) : isEnhanced ? (
          <button
            type="button"
            onClick={handleRevert}
            className="text-muted-foreground hover:text-foreground transition-colors"
            title="Revert to original text"
          >
            <RotateCcw className="h-4 w-4" />
          </button>
        ) : (
          <button
            type="button"
            onClick={handleEnhance}
            className="text-muted-foreground hover:text-foreground transition-colors"
            title="Enhance with AI"
          >
            <Sparkles className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Error message */}
      {error && (
        <div className="text-xs text-destructive mt-1">
          {error}
        </div>
      )}
    </div>
  )
})

EnhancedTextarea.displayName = "EnhancedTextarea"

export { EnhancedTextarea }
