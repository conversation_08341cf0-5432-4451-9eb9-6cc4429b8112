"use client"

import { useState, useEffect, useCallback } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Search, X, Loader2 } from "lucide-react" // Added Loader2
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { formatCount, getFaviconUrl } from "@/lib/utils"
import { Brand } from "@/types" // Import Brand type
import { searchBrandsByName } from "@/services/brandService" // Import the service
import { useDebounce } from "@/hooks/use-debounce" // Assuming a debounce hook exists or can be created

// If use-debounce hook doesn't exist, a simple one can be:
// import { useEffect, useState } from 'react';
// export function useDebounce<T>(value: T, delay: number): T {
//   const [debouncedValue, setDebouncedValue] = useState<T>(value);
//   useEffect(() => {
//     const handler = setTimeout(() => {
//       setDebouncedValue(value);
//     }, delay);
//     return () => {
//       clearTimeout(handler);
//     };
//   }, [value, delay]);
//   return debouncedValue;
// }


export function SearchBrands() {
  const [searchTerm, setSearchTerm] = useState("")
  const debouncedSearchTerm = useDebounce(searchTerm, 300) // Debounce search term
  const [searchResults, setSearchResults] = useState<Brand[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showResults, setShowResults] = useState(false) // To control visibility of results dropdown

  const performSearch = useCallback(async (term: string) => {
    if (term.length < 2) { // Only search if term is 2 or more characters
      setSearchResults([])
      setShowResults(false)
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    setError(null)
    setShowResults(true) // Show dropdown when search starts

    try {
      const results = await searchBrandsByName(term, 10) // Fetch up to 10 results
      setSearchResults(results)
    } catch (err) {
      console.error("Error searching brands:", err)
      setError(err instanceof Error ? err.message : "Failed to search brands")
      setSearchResults([])
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    performSearch(debouncedSearchTerm)
  }, [debouncedSearchTerm, performSearch])

  const handleFocus = () => {
    if (searchTerm.length > 1 && searchResults.length > 0) {
      setShowResults(true)
    }
  }
  
  const handleBlur = () => {
    // Delay hiding results to allow click on result items
    setTimeout(() => {
      setShowResults(false)
    }, 150)
  }

  return (
    <div className="w-full max-w-3xl mx-auto relative">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search for brands..."
          className="pl-10 pr-10 py-6 text-lg" // Added pr-10 for clear button
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onFocus={handleFocus}
          onBlur={handleBlur}
          aria-label="Search for brands"
        />
        {searchTerm && !isLoading && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-2 top-1/2 -translate-y-1/2"
            onClick={() => {
              setSearchTerm("")
              setSearchResults([])
              setShowResults(false)
            }}
            aria-label="Clear search"
          >
            <X className="h-5 w-5" />
          </Button>
        )}
        {isLoading && (
          <Loader2 className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground animate-spin" />
        )}
      </div>

      {showResults && (debouncedSearchTerm.length > 1) && (
        <Card className="absolute w-full mt-1 z-50 max-h-[70vh] overflow-y-auto shadow-lg border">
          <CardContent className="p-2">
            {isLoading && searchResults.length === 0 && ( // Show loader only if no results yet
              <div className="p-4 text-center text-text-secondary">
                <Loader2 className="h-6 w-6 mx-auto animate-spin" />
                <p>Searching...</p>
              </div>
            )}
            {!isLoading && error && (
              <div className="p-4 text-center text-red-500">
                <p>Error: {error}</p>
              </div>
            )}
            {!isLoading && !error && searchResults.length > 0 && (
              <div className="space-y-1">
                {searchResults.map((brand) => (
                  <Link
                    key={brand.id}
                    href={`/${brand.id}`} // Updated link to brand page
                    className="block"
                    onClick={() => setShowResults(false)} // Hide results on click
                  >
                    <div className="flex items-center p-3 hover:bg-muted rounded-md transition-colors">
                      <div className="rounded-lg overflow-hidden bg-muted flex items-center justify-center w-10 h-10 mr-3 shrink-0">
                        <Image
                          src={brand.logo || getFaviconUrl(brand.domain)}
                          alt={`${brand.name} logo`}
                          width={40}
                          height={40}
                          className="object-contain" // Ensure logo fits well
                        />
                      </div>
                      <div className="flex-1 min-w-0"> {/* Added min-w-0 for text truncation */}
                        <h3 className="font-medium truncate">{brand.name}</h3>
                        <div className="flex items-center mt-0.5 space-x-2 text-xs text-text-secondary">
                          <Badge variant="outline" className="text-xs py-0.5 px-1.5">
                            {brand.category || "N/A"}
                          </Badge>
                          <span>·</span>
                          <span>{formatCount(brand.flawaCount)} Flawas</span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
            {!isLoading && !error && searchResults.length === 0 && debouncedSearchTerm.length > 1 && (
              <div className="p-4 text-center text-text-secondary">
                <p>No brands found matching "{debouncedSearchTerm}".</p>
                <Link href="/suggest-brand" passHref>
                  <Button variant="link" className="text-sm mt-1">
                    Suggest this brand?
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
