"use client"

import { useState, useMemo, useEffect } from "react";
import { brands as allBrandsData } from "@/data/brands";
import { Brand } from "@/types";
import { BrandCard } from "@/components/brand-card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
// Tabs components are no longer needed
// import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"; 
import { Search, ArrowDownAZ, ArrowUpAZ } from "lucide-react";

// Metadata cannot be exported from a Client Component.
// It should be defined in a Server Component, like a layout.tsx file.

const getAllBrands = (): Brand[] => {
  return Object.values(allBrandsData);
};

export default function BrandsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  // activeCategory and uniqueCategories are no longer needed
  // const [activeCategory, setActiveCategory] = useState<string | null>(null); 
  const [sortBy, setSortBy] = useState<"flawaCount" | "nameAsc" | "nameDesc">("flawaCount");
  const [brandsToShow, setBrandsToShow] = useState(12);

  // Memoize all brands once
  const allBrands = useMemo(() => getAllBrands(), []);

  // uniqueCategories is removed
  // const uniqueCategories = useMemo(() => {
  //   const categories = new Set(allBrands.map(brand => brand.category));
  //   return Array.from(categories).sort(); 
  // }, [allBrands]);

  // Reset brandsToShow when searchQuery changes (activeCategory removed from dependencies)
  useEffect(() => {
    // Only reset if searchQuery is actually initiating a new search,
    // or to reset to initial state if it becomes empty.
    setBrandsToShow(12);
  }, [searchQuery]);

  // Memoize the count of brands that match current filters (search only, category filter removed)
  const totalMatchingBrandsCount = useMemo(() => {
    return allBrands.filter(brand => {
      const matchesSearch =
        searchQuery === "" ||
        brand.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        brand.category.toLowerCase().includes(searchQuery.toLowerCase()); // Keep category in search
      // const matchesCategory = !activeCategory || brand.category === activeCategory; // Removed
      return matchesSearch; // Only matchesSearch
    }).length;
  }, [allBrands, searchQuery]);

  // Memoize filtered and sorted brands to display (category filter removed)
  const filteredAndSortedBrands = useMemo(() => {
    return allBrands
      .filter(brand => {
        const matchesSearch =
          searchQuery === "" ||
          brand.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          brand.category.toLowerCase().includes(searchQuery.toLowerCase()); // Keep category in search
        // const matchesCategory = !activeCategory || brand.category === activeCategory; // Removed
        return matchesSearch; // Only matchesSearch
      })
      .sort((a, b) => {
        if (sortBy === "flawaCount") {
          return b.flawaCount - a.flawaCount;
        } else if (sortBy === "nameAsc") {
          return a.name.localeCompare(b.name);
        } else if (sortBy === "nameDesc") {
          return b.name.localeCompare(a.name);
        }
        return 0;
      })
      .slice(0, brandsToShow);
  }, [allBrands, searchQuery, sortBy, brandsToShow]); // activeCategory removed from dependencies

  if (!allBrands || allBrands.length === 0) {
    return (
      <section className="w-full py-12 md:py-24 lg:py-32">
        <div className="container px-4 md:px-6 text-center">
          <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
            No Brands Found
          </h1>
          <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed mt-4">
            It seems there are no brands to display at the moment.
          </p>
        </div>
      </section>
    );
  }

  return (
    <section className="w-full py-12 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-10">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
              All Brands
            </h1>
            <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
              Explore every brand featured on Flawagram.
            </p>
          </div>
        </div>

        <div className="space-y-8">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex-1 w-full sm:max-w-sm">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-text-secondary" />
                <Input
                  type="search"
                  placeholder="Search brands by name or category..."
                  className="pl-8 w-full"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2 flex-wrap justify-center sm:justify-end">
              <Button
                variant={sortBy === "flawaCount" ? "default" : "outline"}
                onClick={() => setSortBy("flawaCount")}
              >
                Most Flawas
              </Button>
              <Button
                variant={sortBy === "nameAsc" ? "default" : "outline"}
                onClick={() => setSortBy("nameAsc")}
                className="flex items-center"
                aria-label="Sort by name ascending"
              >
                <ArrowDownAZ className="h-5 w-5" /> {/* Swapped Icon */}
              </Button>
              <Button
                variant={sortBy === "nameDesc" ? "default" : "outline"}
                onClick={() => setSortBy("nameDesc")}
                className="flex items-center"
                aria-label="Sort by name descending"
              >
                <ArrowUpAZ className="h-5 w-5" /> {/* Swapped Icon */}
              </Button>
            </div>
          </div>

          {/* Tabs component and related logic removed */}
          {/* <Tabs
            value={activeCategory || "all"}
            onValueChange={(value) => {
              setActiveCategory(value === "all" ? null : value);
            }}
            className="w-full"
          >
            <TabsList className="flex w-full justify-start overflow-x-auto pb-1 flex-nowrap">
              <TabsTrigger value="all">All Categories</TabsTrigger>
              {uniqueCategories.map(category => (
                <TabsTrigger key={category} value={category}>
                  {category}
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value={activeCategory || "all"} key={activeCategory || "all"} className="mt-4"> */}
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mt-8"> {/* Added mt-8 to replace TabsContent margin */}
                {filteredAndSortedBrands.map((brand) => (
                  <BrandCard key={brand.id} brand={brand} />
                ))}
              </div>
              
              {filteredAndSortedBrands.length > 0 && brandsToShow < totalMatchingBrandsCount && (
                <div className="flex justify-center mt-8">
                  <Button
                    variant="outline"
                    onClick={() => setBrandsToShow(prev => prev + 12)}
                  >
                    Load More Brands
                  </Button>
                </div>
              )}

              {filteredAndSortedBrands.length === 0 && totalMatchingBrandsCount === 0 && searchQuery !== "" && ( // Simplified condition
                <div className="text-center py-10">
                  <p className="text-xl text-text-secondary">No brands match your search.</p>
                </div>
              )}
            {/* </TabsContent>
          </Tabs> */}
        </div>
      </div>
    </section>
  );
}