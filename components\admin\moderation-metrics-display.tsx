'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2, Clock, CheckCircle, ListFilter } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface ModerationPerformancePoint {
  date: string;
  avgHours: number;
}

interface ModerationMetrics {
  averageTimeToModerateHours: number;
  pendingReviewCount: number;
  moderatedLast24Hours: number;
  moderationPerformance: ModerationPerformancePoint[];
}

interface ApiResponse extends Partial<ModerationMetrics> {
  error?: string;
}

export function ModerationMetricsDisplay() {
  const [data, setData] = useState<ModerationMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/admin/moderation-metrics');
        const result: ApiResponse = await response.json();

        if (!response.ok) {
          throw new Error(result.error || `API request failed with status ${response.status}`);
        }
        
        if (result.averageTimeToModerateHours !== undefined && result.pendingReviewCount !== undefined && result.moderatedLast24Hours !== undefined && result.moderationPerformance) {
            setData({
                averageTimeToModerateHours: result.averageTimeToModerateHours,
                pendingReviewCount: result.pendingReviewCount,
                moderatedLast24Hours: result.moderatedLast24Hours,
                moderationPerformance: result.moderationPerformance,
            });
        } else {
            throw new Error("Incomplete moderation metrics data received from API.");
        }

      } catch (e: any) {
        console.error("Failed to fetch moderation metrics:", e);
        setError(e.message || 'An unknown error occurred.');
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Content Moderation Metrics</CardTitle>
          <CardDescription>Overview of content review performance.</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <p className="ml-2">Loading moderation data...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Content Moderation Metrics</CardTitle>
          <CardDescription>Overview of content review performance.</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Content Moderation Metrics</CardTitle>
          <CardDescription>Overview of content review performance.</CardDescription>
        </CardHeader>
        <CardContent>
          <p>No moderation metrics data available to display.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Content Moderation Metrics</CardTitle>
        <CardDescription>Key performance indicators for content review.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-muted/30">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Time to Moderate</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.averageTimeToModerateHours.toFixed(1)} hrs</div>
              <p className="text-xs text-muted-foreground">Average time from submission to review</p>
            </CardContent>
          </Card>
          <Card className="bg-muted/30">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Items Pending Review</CardTitle>
              <ListFilter className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.pendingReviewCount.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Currently awaiting moderation</p>
            </CardContent>
          </Card>
          <Card className="bg-muted/30">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Moderated (Last 24h)</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.moderatedLast24Hours.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Items reviewed in the past day</p>
            </CardContent>
          </Card>
        </div>

        {data.moderationPerformance && data.moderationPerformance.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold mb-2 mt-4">Avg. Moderation Time Trend (Hours)</h3>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data.moderationPerformance} margin={{ top: 5, right: 20, left: 0, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" stroke="#888888" fontSize={12} />
                  <YAxis stroke="#888888" fontSize={12} domain={['dataMin - 1', 'dataMax + 1']} allowDecimals={false} />
                  <Tooltip
                    contentStyle={{ backgroundColor: "hsl(var(--background))", border: "1px solid hsl(var(--border))" }}
                    labelStyle={{ color: "hsl(var(--foreground))" }}
                  />
                  <Legend wrapperStyle={{ paddingTop: '10px' }} />
                  <Line type="monotone" dataKey="avgHours" name="Avg. Time (Hours)" stroke="hsl(var(--primary))" strokeWidth={2} dot={{ r: 4 }} activeDot={{ r: 6 }} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}