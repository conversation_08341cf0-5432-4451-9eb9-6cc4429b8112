"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Flower, Search } from "lucide-react"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { useState } from "react"
import { FlawaCard } from "@/components/ui/flawa-card"
import { Flawa } from "@/types"
import { getAllFlawas } from "@/data/flawas"
import { GlobalFlawaButton } from "@/components/global-flawa-button"

export default function AllFlawasPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilter, setActiveFilter] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState<"recent" | "popular">("recent")
  const [flawasToShow, setFlawasToShow] = useState(12)
  
  // Get all flawas
  const allFlawas = getAllFlawas()
  
  // Filter and sort flawas
  const filteredFlawas = allFlawas
    .filter(flawa => {
      const matchesSearch = searchQuery === "" || 
        flawa.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        flawa.author.toLowerCase().includes(searchQuery.toLowerCase()) ||
        flawa.brand.toLowerCase().includes(searchQuery.toLowerCase())
        
      const matchesFilter = !activeFilter || 
        (activeFilter === "twitter" && flawa.socialLinks?.twitter) ||
        (activeFilter === "instagram" && flawa.socialLinks?.instagram) ||
        (activeFilter === "youtube" && flawa.type === "video")
        
      return matchesSearch && matchesFilter
    })
    .sort((a, b) => {
      if (sortBy === "recent") {
        return new Date(b.timestamp || '').getTime() - new Date(a.timestamp || '').getTime()
      } else {
        return b.likes - a.likes
      }
    })
    .slice(0, flawasToShow)

  const handleShare = async (flawa: Flawa) => {
    const shareUrl = `https://www.flawagram.com/${flawa.brand.toLowerCase()}`
    const shareText = `Check out this amazing flawa about ${flawa.brand} on Flawa! ${flawa.content.substring(0, 100)}...`

    if (navigator.share) {
      try {
        await navigator.share({
          title: `Flawa for ${flawa.brand}`,
          text: shareText,
          url: shareUrl,
        });
        console.log('Flawa shared successfully');
      } catch (error) {
        console.error('Error sharing Flawa:', error);
        // Fallback for when navigator.share fails or is not supported
        // You could open a modal with manual sharing options here
        alert("Sharing failed. Please try copying the link manually.");
      }
    } else {
      // Fallback for browsers that do not support the Web Share API
      // You could open a modal with manual sharing options here
      navigator.clipboard.writeText(shareUrl)
      alert("Web Share API not supported. Link copied to clipboard!");
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        <section className="w-full py-12">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center mb-8">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">All Flawas</h1>
                <p className="mx-auto max-w-[700px] text-text-secondary md:text-xl">
                  Discover all the love and appreciation shared by our community.
                </p>
              </div>
            </div>

            <div className="space-y-8">
              <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div className="flex-1 w-full sm:max-w-sm">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-text-secondary" />
                    <Input
                      type="search"
                      placeholder="Search flawas..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant={sortBy === "recent" ? "default" : "outline"}
                    onClick={() => setSortBy("recent")}
                  >
                    Most Recent
                  </Button>
                  <Button
                    variant={sortBy === "popular" ? "default" : "outline"}
                    onClick={() => setSortBy("popular")}
                  >
                    Most Popular
                  </Button>
                </div>
              </div>

              <Tabs defaultValue="all" className="w-full">
                <TabsList className="w-full justify-start">
                  <TabsTrigger value="all" onClick={() => setActiveFilter(null)}>
                    All
                  </TabsTrigger>
                  <TabsTrigger value="twitter" onClick={() => setActiveFilter("twitter")}>
                    Twitter
                  </TabsTrigger>
                  <TabsTrigger value="instagram" onClick={() => setActiveFilter("instagram")}>
                    Instagram
                  </TabsTrigger>
                  <TabsTrigger value="youtube" onClick={() => setActiveFilter("youtube")}>
                    YouTube
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="all" className="space-y-8">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredFlawas.map((flawa) => (
                      <FlawaCard key={flawa.id} flawa={flawa} onShare={handleShare} />
                    ))}
                  </div>
                  
                  {flawasToShow < allFlawas.length && (
                    <div className="flex justify-center mt-8">
                      <Button
                        variant="outline"
                        onClick={() => setFlawasToShow(prev => prev + 12)}
                      >
                        Load More
                      </Button>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </section>
      </main>
      <GlobalFlawaButton />
    </div>
  )
}