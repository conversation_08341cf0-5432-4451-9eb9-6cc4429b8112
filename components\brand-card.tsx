"use client";

import Link from "next/link";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Flower } from "lucide-react";
import { Brand } from "@/types";
import { formatCount, getFaviconUrl } from "@/lib/utils";

interface BrandCardProps {
  brand: Brand;
}

export function BrandCard({ brand }: BrandCardProps) {
  return (
    <Link key={brand.id} href={`/${brand.id}`} className="hover-scale flex">
      <Card className="overflow-hidden border-border shadow-card rounded-xl h-full w-full flex flex-col">
        <CardContent className="p-6 flex flex-col items-center text-center flex-grow">
          <div className="rounded-xl overflow-hidden bg-white flex items-center justify-center w-16 h-16 mb-4">
            <Image
              src={brand.logo || getFaviconUrl(brand.domain)}
              alt={`${brand.name} logo`}
              width={64}
              height={64}
              className="brand-logo"
              onError={(e) => {
                e.currentTarget.src = getFaviconUrl('example.com'); // A generic fallback
              }}
            />
          </div>
          <div className="flex flex-col flex-grow justify-between">
            <div>
              <h3 className="font-bold text-lg">{brand.name}</h3>
              <div className="flex items-center justify-center mt-2 space-x-2">
                <Badge variant="outline" className="text-xs">
                  {brand.category}
                </Badge>
              </div>
            </div>
            <div className="mt-2 flex items-center justify-center text-text-secondary">
              <Flower className="h-4 w-4 mr-1" />
              <span>{formatCount(brand.flawaCount)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}