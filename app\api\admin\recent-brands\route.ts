import { NextResponse } from 'next/server';
import { fetchRecentBrandsWithFlawaCount } from '@/services/brandService';
import { withAdminAuth } from '@/lib/authUtils';
// import { supabase } from '@/lib/supabaseClient'; // No longer needed here

async function handler(request: Request) { // Renamed from GET to handler
  try {
    // Admin check is now handled by withAdminAuth HOF
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '5', 10);

    if (isNaN(limit) || limit <= 0) {
      return NextResponse.json({ error: 'Invalid limit parameter' }, { status: 400 });
    }

    const recentBrands = await fetchRecentBrandsWithFlawaCount(limit);
    return NextResponse.json(recentBrands);

  } catch (error) {
    console.error('Error in GET /api/admin/recent-brands:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to fetch recent brands', details: errorMessage }, { status: 500 });
  }
}

export const GET = withAdminAuth(handler);