import { NextResponse } from 'next/server';
import {
  fetchTotalBrandsCount,
  fetchPendingBrandsCount,
  fetchBrandsCreatedInMonthCount,
} from '@/services/brandService';
import {
  fetchTotalFlawasCount,
  fetchPendingFlawasCount,
  fetchFlawasSubmittedInMonthCount,
  fetchFlawasModeratedTodayCount,
} from '@/services/flawaService';
import {
  fetchPendingBrandSuggestionsCount,
  fetchBrandSuggestionsReviewedTodayCount,
} from '@/services/brandSuggestionService';
import { withAdminAuth } from '@/lib/authUtils';
// import { supabase } from '@/lib/supabaseClient'; // No longer needed here for admin check

async function handler(request: Request) { // Renamed from GET to handler for withAdminAuth
  try {
    // Admin check is now handled by withAdminAuth HOF
    const today = new Date();

    // Fetch all stats using the service functions
    const [
      totalBrands,
      pendingBrands,
      totalFlawas,
      pendingFlawas,
      brandsChange,
      flawasChange,
      flawasModeratedToday,
      pendingBrandSuggestions,
      suggestionsReviewedToday,
    ] = await Promise.all([
      fetchTotalBrandsCount(),
      fetchPendingBrandsCount(),
      fetchTotalFlawasCount(),
      fetchPendingFlawasCount(),
      fetchBrandsCreatedInMonthCount(today),
      fetchFlawasSubmittedInMonthCount(today),
      fetchFlawasModeratedTodayCount(today),
      fetchPendingBrandSuggestionsCount(),
      fetchBrandSuggestionsReviewedTodayCount(today),
    ]);

    const stats = {
      totalBrands,
      pendingBrands,
      totalFlawas,
      pendingFlawas,
      brandsChange,
      flawasChange,
      flawasModeratedToday,
      pendingBrandSuggestions,
      suggestionsReviewedToday,
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Error in GET /api/admin/stats:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to fetch admin statistics', details: errorMessage }, { status: 500 });
  }
}

export const GET = withAdminAuth(handler);