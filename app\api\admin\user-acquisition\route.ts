import { supabase } from '@/lib/supabaseClient';
import { NextRequest, NextResponse } from 'next/server';

// TODO: Implement proper admin role check
// For now, this is a placeholder. In a real app, you'd verify
// the user's session and check if they have an admin role.
const isAdmin = (req: NextRequest) => {
  // Replace with actual admin check logic
  // For example, check a custom claim in the JWT, or query a 'roles' table.
  // const { data: { user } } = await supabase.auth.getUser();
  // return user?.app_metadata?.roles?.includes('admin');
  console.log("Admin check placeholder for request:", req.url);
  return true; // Placeholder: assumes admin for now
};

export async function GET(req: NextRequest) {
  if (!isAdmin(req)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Fetch all users with their creation dates
    // Supabase auth.users table has 'created_at'
    const { data: users, error: usersError } = await supabase
      .from('users') // Assuming you are querying the auth.users table.
                     // If you have a public schema table 'users' that mirrors auth.users, use that.
                     // For direct auth.users access, you might need service_role key or specific RLS policies.
                     // Let's assume for now 'users' is a view or table accessible by the anon key with RLS for admins,
                     // or this is running in a context where service_role key is used (e.g. server-side with admin privileges).
      .select('id, created_at')
      .order('created_at', { ascending: true });

    if (usersError) {
      console.error('Error fetching users:', usersError);
      return NextResponse.json({ error: `Error fetching users: ${usersError.message}` }, { status: 500 });
    }

    if (!users || users.length === 0) {
      return NextResponse.json({ trends: [], message: 'No user data found.' }, { status: 200 });
    }

    // Process data to get daily, weekly, or monthly trends
    // For simplicity, let's do daily trends for now
    const trends = users.reduce((acc, user) => {
      if (user.created_at) {
        const date = new Date(user.created_at).toISOString().split('T')[0]; // YYYY-MM-DD
        acc[date] = (acc[date] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const formattedTrends = Object.entries(trends)
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    return NextResponse.json({ trends: formattedTrends }, { status: 200 });
  } catch (error: any) {
    console.error('Error in user acquisition API:', error);
    return NextResponse.json({ error: `Internal server error: ${error.message}` }, { status: 500 });
  }
}