import Link from "next/link"
import { Flower } from "lucide-react"

export function Footer() {
  return (
    <footer className="w-full border-t py-12 md:py-16">
      <div className="container px-4 md:px-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8">
          <div className="space-y-4 text-center sm:text-left">
            <Link href="/" className="flex items-center space-x-2 justify-center sm:justify-start">
              <Flower className="h-5 w-5" />
              <span className="text-xl font-bold">Flawagram</span>
            </Link>
            <p className="text-sm text-text-secondary">
              Where brands celebrate all the love they receive — collected in one beautiful, curated space.
            </p>
          </div>
          <div className="space-y-4 text-center sm:text-left">
            <h3 className="font-medium text-lg">For Brands</h3>
            <ul className="space-y-3 flex flex-col items-center sm:items-start">
              <li>
                <Link href="/pricing" className="text-sm text-text-secondary hover:text-hover transition-colors">
                  Pricing
                </Link>
              </li>
              <li>
                <Link href="/features" className="text-sm text-text-secondary hover:text-hover transition-colors">
                  Features
                </Link>
              </li>
              <li>
                <Link
                  href="/claim"
                  className="inline-flex items-center text-sm font-medium bg-black text-white hover:bg-gray-800 transition-colors px-4 py-2 rounded-md"
                >
                  <Flower className="mr-2 h-4 w-4" />
                  <span>Claim Your Brand</span>
                </Link>
              </li>
            </ul>
          </div>
          <div className="space-y-4 text-center sm:text-left">
            <h3 className="font-medium text-lg">Resources</h3>
            <ul className="space-y-3 flex flex-col items-center sm:items-start">
              <li>
                <Link href="/about" className="text-sm text-text-secondary hover:text-hover transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-sm text-text-secondary hover:text-hover transition-colors">
                  Blog
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-sm text-text-secondary hover:text-hover transition-colors">
                  Contact
                </Link>
              </li>
            </ul>
          </div>
          <div className="space-y-4 text-center sm:text-left">
            <h3 className="font-medium text-lg">Legal</h3>
            <ul className="space-y-3 flex flex-col items-center sm:items-start">
              <li>
                <Link href="/privacy" className="text-sm text-text-secondary hover:text-hover transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-sm text-text-secondary hover:text-hover transition-colors">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link href="/cookies" className="text-sm text-text-secondary hover:text-hover transition-colors">
                  Cookie Policy
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="mt-8 pt-8 border-t flex flex-col md:flex-row justify-between items-center text-center md:text-left">
          <p className="text-xs text-text-secondary"> {new Date().getFullYear()} Flawagram. All rights reserved.</p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <Link href="#" className="text-text-secondary hover:text-hover transition-colors">
              <span className="sr-only">Twitter</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-twitter"
              >
                <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
              </svg>
            </Link>
            <Link href="#" className="text-text-secondary hover:text-hover transition-colors">
              <span className="sr-only">Instagram</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-instagram"
              >
                <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
              </svg>
            </Link>
            <Link href="#" className="text-text-secondary hover:text-hover transition-colors">
              <span className="sr-only">LinkedIn</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-linkedin"
              >
                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
                <rect width="4" height="12" x="2" y="9" />
                <circle cx="4" cy="4" r="2" />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
