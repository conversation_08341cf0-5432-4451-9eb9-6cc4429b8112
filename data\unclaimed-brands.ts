import { Brand } from "@/types"

export const unclaimedBrands: Record<string, Brand> = {
  spotify: {
    id: "spotify",
    name: "Spotify",
    logo: null,
    domain: "spotify.com",
    description:
      "Spotify is a digital music, podcast, and video streaming service that gives you access to millions of songs and other content from artists all over the world.",
    website: "https://spotify.com",
    category: "Entertainment",
    flawaCount: 756,
    isClaimed: false,
    tier: "Free",
  },
  airbnb: {
    id: "airbnb",
    name: "Airbnb",
    logo: null,
    domain: "airbnb.com",
    description:
      "Airbnb, Inc. is an American company that operates an online marketplace for lodging, primarily homestays for vacation rentals, and tourism activities.",
    website: "https://airbnb.com",
    category: "Travel",
    flawaCount: 531,
    isClaimed: false,
    tier: "Free",
  },
}