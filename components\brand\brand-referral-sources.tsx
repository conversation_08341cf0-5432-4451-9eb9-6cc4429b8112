'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2 } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell } from 'recharts';

interface ReferralSource {
  source: string;
  views: number;
  uniqueVisitors: number;
}

interface ApiResponse {
  referralSources?: ReferralSource[];
  error?: string;
}

interface BrandReferralSourcesProps {
  brandId: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82Ca9D', '#A4DE6C', '#D0ED57', '#FFC658'];


export function BrandReferralSources({ brandId }: BrandReferralSourcesProps) {
  const [data, setData] = useState<ReferralSource[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!brandId) {
      setError("Brand ID is missing.");
      setLoading(false);
      return;
    }

    async function fetchData() {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/brands/${brandId}/referral-sources`);
        const result: ApiResponse = await response.json();

        if (!response.ok) {
          throw new Error(result.error || `API request failed with status ${response.status}`);
        }
        
        setData(result.referralSources || []);

      } catch (e: any) {
        console.error(`Failed to fetch referral sources for brand ${brandId}:`, e);
        setError(e.message || 'An unknown error occurred.');
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [brandId]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Referral Sources</CardTitle>
          <CardDescription>Where your brand page visitors are coming from.</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <p className="ml-2">Loading referral data...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Referral Sources</CardTitle>
          <CardDescription>Where your brand page visitors are coming from.</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Referral Sources</CardTitle>
          <CardDescription>Where your brand page visitors are coming from.</CardDescription>
        </CardHeader>
        <CardContent>
          <p>No referral data available for this brand yet.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Top Referral Sources</CardTitle>
        <CardDescription>Breakdown of visitors by referring domain or source.</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-8 h-[350px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} layout="vertical" margin={{ top: 5, right: 30, left: 50, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" stroke="#888888" fontSize={12} />
              <YAxis dataKey="source" type="category" stroke="#888888" fontSize={12} width={100} tickLine={false} axisLine={false} />
              <Tooltip
                contentStyle={{ backgroundColor: "hsl(var(--background))", border: "1px solid hsl(var(--border))" }}
                labelStyle={{ color: "hsl(var(--foreground))" }}
                formatter={(value: number, name: string) => [value.toLocaleString(), name === 'views' ? 'Page Views' : 'Unique Visitors']}
              />
              <Legend wrapperStyle={{ paddingTop: '10px' }} />
              <Bar dataKey="views" name="Page Views" barSize={20}>
                 {data.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
              </Bar>
              {/* Optional: Add unique visitors as another bar or in tooltip */}
            </BarChart>
          </ResponsiveContainer>
        </div>
        
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Source</TableHead>
              <TableHead className="text-right">Page Views</TableHead>
              <TableHead className="text-right">Unique Visitors</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((item) => (
              <TableRow key={item.source}>
                <TableCell className="font-medium">{item.source}</TableCell>
                <TableCell className="text-right">{item.views.toLocaleString()}</TableCell>
                <TableCell className="text-right">{item.uniqueVisitors.toLocaleString()}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}