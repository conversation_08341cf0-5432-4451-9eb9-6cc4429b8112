"use client"

import { useState, FormEvent } from 'react'
import { supabase } from '@/lib/supabaseClient'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'
import { AuthError, Session } from '@supabase/supabase-js'

interface AuthFormProps {
  onAuthSuccess?: (session: Session | null) => void // Callback on successful auth
}

export default function AuthForm({ onAuthSuccess }: AuthFormProps) {
  const [isSignUp, setIsSignUp] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleAuth = async (event: FormEvent) => {
    event.preventDefault()
    setLoading(true)
    setError(null)

    try {
      let authError: AuthError | null = null
      let session: Session | null = null

      if (isSignUp) {
        const { data, error: signUpError } = await supabase.auth.signUp({
          email,
          password,
        })
        authError = signUpError
        if (data.session) session = data.session
        if (!signUpError && data.user) {
          toast.success('Signup successful! Please check your email to verify your account.')
           // For Supabase, email verification is usually required.
           // The session might be null until verification or if auto-confirm is off.
          if (onAuthSuccess && data.session) onAuthSuccess(data.session)
          else if (onAuthSuccess) onAuthSuccess(null) // Indicate signup initiated
        }
      } else {
        const { data, error: signInError } = await supabase.auth.signInWithPassword({
          email,
          password,
        })
        authError = signInError
        if (data.session) session = data.session
        if (!signInError && data.session) {
          toast.success('Login successful!')
          if (onAuthSuccess) onAuthSuccess(data.session)
        }
      }

      if (authError) {
        setError(authError.message)
        toast.error(authError.message)
      }
    } catch (catchError: any) {
      setError('An unexpected error occurred. Please try again.')
      toast.error('An unexpected error occurred.')
      console.error("Auth error:", catchError)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>{isSignUp ? 'Create an Account' : 'Welcome Back'}</CardTitle>
        <CardDescription>
          {isSignUp ? 'Enter your email and password to sign up.' : 'Enter your credentials to log in.'}
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleAuth}>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={loading}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={loading}
              minLength={isSignUp ? 6 : undefined} // Supabase default min password length
            />
            {isSignUp && <p className="text-xs text-muted-foreground">Password should be at least 6 characters.</p>}
          </div>
          {error && <p className="text-sm text-red-500">{error}</p>}
        </CardContent>
        <CardFooter className="flex flex-col gap-4">
          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? (isSignUp ? 'Signing Up...' : 'Logging In...') : (isSignUp ? 'Sign Up' : 'Log In')}
          </Button>
          <Button
            type="button"
            variant="link"
            onClick={() => {
              setIsSignUp(!isSignUp)
              setError(null) // Clear error when switching mode
            }}
            disabled={loading}
          >
            {isSignUp ? 'Already have an account? Log In' : "Don't have an account? Sign Up"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}