import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Format numeric count (e.g., 1000 -> 1K)
export const formatCount = (count: number): string => {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1).replace(/\.0$/, "")}M`
  }
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1).replace(/\.0$/, "")}K`
  }
  return count.toString()
}

// Get favicon URL as a fallback
export const getFaviconUrl = (domain: string, size = 128): string => {
  // Ensure the domain is just the hostname, no protocol or path
  let hostname = domain;
  try {
    const url = new URL(domain.startsWith('http') ? domain : `http://${domain}`);
    hostname = url.hostname;
  } catch (e) {
    // If domain is not a valid URL (e.g., just "example.com"), use it as is
    // console.warn(`Invalid domain for favicon: ${domain}`, e);
  }
  return `https://www.google.com/s2/favicons?domain=${hostname}&sz=${size}`
}
